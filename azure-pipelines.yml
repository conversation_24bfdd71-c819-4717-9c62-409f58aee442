# ASP.NET
# Build and test ASP.NET projects.
# Add steps that publish symbols, save build artifacts, deploy, and more:
# https://docs.microsoft.com/azure/devops/pipelines/apps/aspnet/build-aspnet-4

pool: 'Azure Pipelines'
variables:
  buildConfiguration: 'Release'
  buildPlatform: 'any cpu'
  projectPath: '$(Build.SourcesDirectory)/src/Fiestr.Web/Fiestr.Web.csproj'
  azureSubscription: 'Azure subscription 1(4898dc34-9546-4a31-9583-f2c4bc7735e5)' # Update this with your Azure Service Connection name
  appServiceName: 'FiestrApi' # Update this with your Azure App Service name
  sshEndpoint: 'FiestrMainVM_SSH'

stages:
- stage: Build
  displayName: 'Build Stage'
  jobs:
  - job: BuildJob
    displayName: 'Build'
    steps:
    - task: NuGetToolInstaller@1
      displayName: 'Install NuGet'

    - task: UseDotNet@2
      displayName: 'Install .NET'
      inputs:
        packageType: 'sdk'
        version: '8.x'
        installationPath: $(Agent.ToolsDirectory)/dotnet

    - task: DotNetCoreCLI@2
      displayName: 'Restore NuGet Packages'
      inputs:
        command: 'restore'
        projects: '$(projectPath)'

    - task: DotNetCoreCLI@2
      displayName: 'Build Project'
      inputs:
        command: 'build'
        projects: '$(projectPath)'
        arguments: '--configuration $(buildConfiguration) --no-restore'

    - task: DotNetCoreCLI@2
      displayName: 'Publish Project'
      inputs:
        command: 'publish'
        publishWebProjects: true
        arguments: '--configuration $(buildConfiguration) --output $(Build.ArtifactStagingDirectory)'
        zipAfterPublish: true

    - task: CopyFiles@2
      displayName: 'Copy Scripts to Artifacts'
      inputs:
        SourceFolder: 'scripts'
        Contents: '**/*'
        TargetFolder: '$(Build.ArtifactStagingDirectory)/scripts'

    - task: PublishBuildArtifacts@1
      displayName: 'Publish Artifacts'
      inputs:
        PathtoPublish: '$(Build.ArtifactStagingDirectory)'
        ArtifactName: 'drop'
        publishLocation: 'Container'

- stage: Deploy
  displayName: 'Deployment Stage'
  dependsOn: Build
  condition: succeeded()
  jobs:
  - deployment: DeploymentJob
    displayName: 'Deploy to Azure VM'
    environment: 'production'
    strategy:
      runOnce:
        deploy:
          steps:

          - task: DownloadBuildArtifacts@0
            displayName: 'Download Artifacts'
            inputs:
              buildType: 'current'
              downloadType: 'single'
              artifactName: 'drop'
              downloadPath: '$(System.ArtifactsDirectory)'

          - task: SSH@0
            displayName: 'Ensure Directory & Permissions'
            inputs:
              sshEndpoint: $(sshEndpoint)
              runOptions: 'inline'
              inline: |
                sudo mkdir -p /var/www/FiestrBackend
                sudo chown -R azureuser:azureuser /var/www/FiestrBackend
                sudo chmod -R 755 /var/www/FiestrBackend

          - task: CopyFilesOverSSH@0
            displayName: 'Copy Artifacts to VM'
            inputs:
              sshEndpoint: $(sshEndpoint)
              sourceFolder: '$(System.ArtifactsDirectory)/drop'
              targetFolder: '/var/www/FiestrBackend'
              cleanTargetFolder: true
              overwrite: true
              readyTimeout: '20000'
              contents: '**/*'

          - task: SSH@0
            displayName: 'Run Bootstrap Script'
            inputs:
              sshEndpoint: $(sshEndpoint)
              runOptions: 'inline'
              inline: |
                chmod +x /var/www/FiestrBackend/scripts/bootstrap.sh
                /var/www/FiestrBackend/scripts/bootstrap.sh

          - task: SSH@0
            displayName: 'Extract & Restart API'
            inputs:
              sshEndpoint: $(sshEndpoint)
              runOptions: 'inline'
              inline: |
                cd /var/www/FiestrBackend
                unzip -oq *.zip -d published
                sudo systemctl restart FiestrBackend
                sudo systemctl status FiestrBackend --no-pager
                sudo rm -f /var/www/FiestrBackend/*.zip
