using FluentValidation;
using FluentValidation.Validators;
namespace Fiestr.Web.Validators;

public class ImageFormatValidator<T>(IEnumerable<string> supportedFormats) : PropertyValidator<T, IFormFile?>
{
  public override bool IsValid(ValidationContext<T> context, IFormFile? value)
  {
    return value is not null && supportedFormats.Any(e => string.Equals(e, value.ContentType, StringComparison.InvariantCultureIgnoreCase));
  }
  
  public override string Name { get; } = "FormFileImageValidator";

  protected override string GetDefaultMessageTemplate(string errorCode) 
    => "{PropertyName} format is invalid";
}
