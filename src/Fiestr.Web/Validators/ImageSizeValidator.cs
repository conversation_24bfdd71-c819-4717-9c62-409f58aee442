using FluentValidation;
using FluentValidation.Validators;
using Humanizer.Bytes;
namespace Fiestr.Web.Validators;

public class ImageSizeValidator<T>(int minBytes, long maxBytes) : PropertyValidator<T, IFormFile?>
{
  public override bool IsValid(ValidationContext<T> context, IFormFile? value)
  {
    if (value is null || value.Length <= minBytes || value.Length >= maxBytes)
    {
      context.MessageFormatter.AppendArgument("MinBytes", Humanizer.ByteSizeExtensions.Humanize(new ByteSize(minBytes)));
      context.MessageFormatter.AppendArgument("MaxBytes", Humanizer.ByteSizeExtensions.Humanize(new ByteSize(maxBytes)));
      return false;
    }

    return true;
  }
  
  public override string Name => "FormFileImageValidator";

  protected override string GetDefaultMessageTemplate(string errorCode) 
    => "{PropertyName} size must be between {MinBytes} and {MaxBytes} bytes";
}
