using Fiestr.Web.Endpoints.Providers.Search;
using Fiestr.Web.Interfaces;
using FluentValidation;
namespace Fiestr.Web.Validators;

public static class RuleBuilderExtensions 
{
  public static IRuleBuilderOptions<T, IFormFile?> ImageSizeValidator<T>(this IRuleBuilder<T, IFormFile?> ruleBuilder, int minBytes, long maxBytes) {
    return ruleBuilder.SetValidator(new ImageSizeValidator<T>(minBytes, maxBytes));
  }
  
  public static IRuleBuilderOptions<T, IFormFile?> ImageFormatValidator<T>(this IRuleBuilder<T, IFormFile?> ruleBuilder, IEnumerable<string> supportedFormats) {
    return ruleBuilder.SetValidator(new ImageFormatValidator<T>(supportedFormats));
    
  }  public static IRuleBuilderOptions<T, IPagedRequest?> PagedRequestValidator<T>(this IRuleBuilder<T, IPagedRequest?> ruleBuilder, int maxElems = 100) {
    return ruleBuilder.SetValidator(new PagedRequestValidator<T>(maxElems));
  }
}
