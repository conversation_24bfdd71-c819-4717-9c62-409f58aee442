using Fiestr.Web.Endpoints.Providers.Search;
using Fiestr.Web.Interfaces;
using FluentValidation;
using FluentValidation.Validators;

namespace Fiestr.Web.Validators;

public class PagedRequestValidator<T>(int maxElems) : PropertyValidator<T, IPagedRequest?>
{
  public override bool IsValid(ValidationContext<T> context, IPagedRequest? value)
  {
    return value is not null && value.PageNumber > 0 && value.PageSize <= maxElems;
  }
  
  public override string Name => "PagedRequestValidator";
  protected override string GetDefaultMessageTemplate(string errorCode) 
    => "Invalid pagination parameters, ensure pageNumber is > 0 and pageSize does not exceed 100";

}
