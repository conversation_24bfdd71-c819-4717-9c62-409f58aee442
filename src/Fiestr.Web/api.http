# For more info on HTTP files go to https://aka.ms/vs/httpfile
@host=https://localhost
@port=5001

// List all contributors
GET {{host}}:{{port}}/Contributors

###

// Get a specific contributor
@id_to_get=1
GET {{host}}:{{port}}/Contributors/{{id_to_get}}

###

// Add a new contributor
POST {{host}}:{{port}}/Contributors
Content-Type: application/json

{
  "name": "John Doe 2",
  "email": "<EMAIL>",
  "phoneNumber": "1234567890"
}

###

// Update a contributor
@id_to_update=1
PUT {{host}}:{{port}}/Contributors/{{id_to_update}}
Content-Type: application/json

{
  "id": {{id_to_update}},
  "name": "ardalis2"
}

###

// Delete a contributor
@id_to_delete=1
DELETE {{host}}:{{port}}/Contributors/{{id_to_delete}}

