using System.Security.Claims;
using Fiestr.Core;
using Fiestr.Core.Interfaces;
namespace Fiestr.Web.Auth;

public class IdentityUser : IIdentityUser
{
  private readonly Lazy<Dictionary<string, Claim>> _lazyClaims;
  public IdentityUser(IHttpContextAccessor ctxAccessor)
  {
    _lazyClaims = new Lazy<Dictionary<string, Claim>>(() => ctxAccessor.HttpContext?.User.Claims.ToDictionary(e => e.Type) ?? []);
  }

  public Guid Id => Guid.Parse(_lazyClaims.Value[Consts.JwtKeys.Sub].Value);

  public int[] LinkedProviderIds =>
    _lazyClaims.Value[Consts.JwtKeys.Sub].Value.Split(',').Select(e => Convert.ToInt32(e)).ToArray();
}
