using FastEndpoints;
using Fiestr.UseCases.Mgmt;

namespace Fiestr.Web.Endpoints.Mgmt.UploadProvider;

public class UploadProviderEndpoint(IImportService service) : Endpoint<Request, Response>
{
  public override void Configure()
  {
    this.Post("mgmt/uploadProviders");
    AllowFileUploads();
    this.AllowAnonymous();
  }

  public override async Task HandleAsync(Request req, CancellationToken ct)
  {
    var result = new Dictionary<string, int>();
    foreach (var file in req.Files)
    {
      var providerId = service.ImportProvider(file.OpenReadStream());
      result.Add(file.FileName, providerId);
    }
    var response = new Response(result);
    await SendOkAsync(response, ct);
  }
}
public record Request(List<IFormFile> Files);
public record Response(Dictionary<string, int> ProviderIds);
