using FastEndpoints;
using Fiestr.UseCases.Mgmt;

namespace Fiestr.Web.Endpoints.Mgmt.DeleteProvider;

public class Endpoint(IImportService service) : Endpoint<Request>
{
  public override void Configure()
  {
    this.Delete("mgmt/deleteProvider/{providerId:int}");
    this.AllowAnonymous();
  }

  public override Task HandleAsync(Request req, CancellationToken ct)
  {
    service.DeleteProvider(req.ProviderId);
    return Task.CompletedTask;
  }
}
public record Request(int ProviderId);
