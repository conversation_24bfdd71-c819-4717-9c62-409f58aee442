using FastEndpoints;
using Fiestr.UseCases.Mgmt;

namespace Fiestr.Web.Endpoints.Mgmt.UploadTags;

public class Endpoint(IImportService service) : Endpoint<Request>
{
  public override void Configure()
  {
    this.Post("mgmt/uploadTags");
    AllowFileUploads();
    this.AllowAnonymous();
  }

  public override Task HandleAsync(Request req, CancellationToken ct)
  {
    service.ImportTags(req.File.OpenReadStream());
    return Task.CompletedTask;
  }
}
public record Request(IFormFile File);
