// using System.Text.RegularExpressions;
// using Ardalis.Result;
// using FastEndpoints;
// using Fiestr.Infrastructure.Data.Config;
// using Fiestr.Infrastructure.ImageProcessing;
// using Fiestr.UseCases.Providers.Create;
// using Fiestr.UseCases.Providers.SetImage;
// using Fiestr.Web.Extensions;
// using Fiestr.Web.Validators;
// using FluentValidation;
// using MediatR;
// using Microsoft.Extensions.Options;
// namespace Fiestr.Web.Endpoints.Providers.SetImage;
//
// public class Endpoint(ISender mediator) : Endpoint<Request>
// {
//   public override void Configure()
//   {
//     Put("/providers/{id}/image");
//     AllowFileUploads();
//   }
//
//   public override async Task HandleAsync(Request r, CancellationToken ct)
//   {
//       var imgCommand = new SetProviderImageCommand(r.Id, r.Image!.OpenReadStream());
//       var result = await mediator.Send(imgCommand, ct);
//       if (result.Status == ResultStatus.NotFound)
//       {
//         await this.SendNotFoundAsync(ct);
//         return;
//       }
//       await this.SendNoContentAsync(ct);
//   }
// }
//
// public record Request(int Id, IFormFile? Image);
//
// public class Validator : Validator<Request>
// {
//   public Validator(IOptions<ImageConfiguration> imgCfg)
//   {
//     RuleFor(x => x.Image)
//       .NotEmpty()
//       .ImageSizeValidator(imgCfg.Value.MinBytes, imgCfg.Value.MaxBytes)
//       .ImageFormatValidator(imgCfg.Value.SupportedFormats);
//   }
// }
