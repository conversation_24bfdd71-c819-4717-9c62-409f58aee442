// using System.Text.RegularExpressions;
// using FastEndpoints;
// using Fiestr.Infrastructure.Data.Config;
// using Fiestr.UseCases.Providers.Create;
// using Fiestr.UseCases.Providers.SetImage;
// using Fiestr.Web.Extensions;
// using FluentValidation;
// using MediatR;
// namespace Fiestr.Web.Endpoints.Providers.Create;
//
// public class Endpoint(ISender mediator) : Endpoint<Request>
// {
//   public override void Configure()
//   {
//     Post("/providers");
//     AllowFileUploads();
//   }
//
//   public override async Task HandleAsync(Request r, CancellationToken ct)
//   {
//     var command = new CreateProviderCommand(r.Name!, r.PhoneNumber!, r.Address1!, r.TagIds!);
//     var providerResult = await mediator.Send(command, ct);
//     if (!providerResult.IsSuccess)
//     {
//       await this.SendResultResponse(providerResult, e => e.Value, ct);
//       return;
//     }
//
//     if (r.Image is not null)
//     {
//       var imgCommand = new SetProviderImageCommand(providerResult.Value, r.Image.OpenReadStream());
//       var imgResult = await mediator.Send(imgCommand, ct);
//       if (!imgResult.IsSuccess)
//       {
//         await this.SendResultResponse(imgResult, e => e.Value, ct);
//         return;
//       }
//     }
//     
//     await this.SendCreatedAtAsync<GetById.Endpoint>(new { Id = providerResult.Value }, null, cancellation: ct);
//   }
// }
//
// public record Request(string? Name, string? PhoneNumber, string? Address1, List<int>? TagIds, IFormFile? Image);
//
// public class Validator : Validator<Request>
// {
//   public Validator()
//   {
//     RuleFor(x => x.Name)
//       .NotEmpty()
//       .MaximumLength(DataSchemaConstants.DEFAULT_NAME_LENGTH);
//
//     RuleFor(x => x.PhoneNumber)
//       .NotEmpty()
//       .Must(e => IsValidMexicanPhoneNumber(e!)).WithMessage("Invalid phone number.");
//     
//     RuleFor(x => x.Address1)
//       .NotEmpty()
//       .MaximumLength(DataSchemaConstants.DEFAULT_NAME_LENGTH);
//
//     RuleFor(x => x.TagIds)
//       .NotNull();
//
//     When(e => e.Image != null, () =>
//     {
//       RuleFor(x => x.Image)
//         .Must(x => IsAllowedSize(x!.Length)).WithMessage("too small!")
//         .Must(x => IsAllowedType(x!.ContentType)).WithMessage("file type is invalid!");
//     });
//
//
//   }
//   
//   public bool IsAllowedType(string contentType)
//     => (new[] { "image/jpeg", "image/png" }).Contains(contentType.ToLower());
//
//   public bool IsAllowedSize(long fileLength)
//     => fileLength is >= 100 and <= 10485760;
//   
//   private const string MexicanPhoneNumberPattern = @"^(?:\d{10}|\d{13})$";
//   private static bool IsValidMexicanPhoneNumber(string phoneNumber)
//   {
//     var normalizedPhoneNumber = Regex.Replace(phoneNumber, @"[^\d]", "");
//     return Regex.IsMatch(normalizedPhoneNumber, MexicanPhoneNumberPattern);
//   }
// }
