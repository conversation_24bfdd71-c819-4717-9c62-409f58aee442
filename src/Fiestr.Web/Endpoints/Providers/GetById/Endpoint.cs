using FastEndpoints;
using Fiestr.UseCases.Providers;
using Fiestr.UseCases.Providers.GetById;
using Fiestr.Web.Extensions;
using FluentValidation;
using MediatR;
namespace Fiestr.Web.Endpoints.Providers.GetById;

public class Endpoint(ISender mediatr) : Endpoint<Request, ProviderSearchDto>
{
  public override void Configure()
  {
    Get("/providers/{id}");
    AllowAnonymous();
  }

  public override async Task HandleAsync(Request r, CancellationToken ct)
  {
    var command = new ProviderByIdQuery(r.Id!.Value);
    var result = await mediatr.Send(command, ct);
    await this.SendResultResponse(result, e => e.Value, ct);
  }
}

public class Request
{
  public int? Id { get; set; }
}

public class Validator : Validator<Request>
{
  public Validator()
  {
    this.RuleFor(e => e.Id)
      .NotEmpty();
  }
}
