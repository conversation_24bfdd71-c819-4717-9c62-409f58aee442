using Ardalis.Result;
using FastEndpoints;
using Fiestr.UseCases.Providers;
using Fiestr.UseCases.Providers.Search;
using Fiestr.Web.Extensions;
using Fiestr.Web.Interfaces;
using Fiestr.Web.Validators;
using FluentValidation;
using MediatR;
namespace Fiestr.Web.Endpoints.Providers.Search;

public class Endpoint(ISender mediatr) : Endpoint<Request, ProviderSearchDto>
{
  public override void Configure()
  {
    Get("/providers");
    AllowAnonymous();
  }

  public override async Task HandleAsync(Request r, CancellationToken ct)
  {
    var command = new ProviderSearchQuery
    {
      Id = r.Id,
      SearchTerm = r.SearchTerm,
      TagIds = r.TagIds ?? [],
      OrderBy = r.OrderBy,
      OrderType = r.OrderType,
      PageNumber = r.PageNumber ?? 1,
      PageSize = r.PageSize ?? 10,
      MinRating = r.MinRating,
      MaxRating = r.MaxRating
    };
    var result = await mediatr.Send(command, ct);
    await this.SendResultResponse(result, e => e.Value, ct);
  }
}

public class Request: IPagedRequest
{
  public int? Id { get; set; }
  public string? SearchTerm { get; set; }
  public List<int>? TagIds { get; set; } = [];
  public int? PageNumber { get; set; } = 1;
  public int? PageSize { get; set; } = 10;
  public string? OrderBy { get; set; }
  public string? OrderType { get; set; }
  public int? MinRating { get; set; }
  public int? MaxRating { get; set; }
}

public class Validator : Validator<Request>
{
  public Validator()
  {
    this.RuleFor(e => e.SearchTerm)
      .MaximumLength(50);

    this.RuleFor(e => e as IPagedRequest)
      .PagedRequestValidator();
  }
}
