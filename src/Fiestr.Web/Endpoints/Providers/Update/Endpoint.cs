// using Ardalis.Result;
// using FastEndpoints;
// using Fiestr.Infrastructure.Data.Config;
// using Fiestr.UseCases.Providers.Update;
// using Fiestr.Web.Extensions;
// using FluentValidation;
// using MediatR;
// namespace Fiestr.Web.Endpoints.Providers.Update;
//
// public class Endpoint(ISender sender) : Endpoint<Request>
// {
//   public override void Configure()
//   {
//     Put("providers/{id}");
//   }
//
//   public override async Task HandleAsync(Request req, CancellationToken ct)
//   {
//     var command = new ProviderUpdateCommand(req.Id, req.Name!, req.PhoneNumber!, req.Address1!, req.TagIds!);
//     var result = await sender.Send(command, ct);
//     if (result.Status == ResultStatus.NotFound)
//     {
//       await this.SendNotFoundAsync(ct);
//       return;
//     }
//     await this.SendResultResponse(result, e => e, ct);
//   }
// }
//
// public record Request
// {
//   public int Id { get; init; }
//   public string? Name { get; init; }
//   public string? PhoneNumber { get; init; }
//   public string? Address1 { get; init; }
//   public List<int>? TagIds { get; init; }
// }
//
// public class Validator : Validator<Request>
// {
//   public Validator()
//   {
//     RuleFor(e => e.Id)
//       .NotEmpty();
//     
//     RuleFor(x => x.Name)
//       .NotEmpty()
//       .MaximumLength(DataSchemaConstants.DEFAULT_NAME_LENGTH);
//
//
//     RuleFor(x => x.PhoneNumber)
//       .NotEmpty();
//     
//     RuleFor(x => x.PhoneNumber)
//       .NotEmpty()
//       .MaximumLength(DataSchemaConstants.DEFAULT_ADDRESS_LENGTH);
//
//     RuleFor(x => x.TagIds)
//       .NotNull();
//   }
// }
