using FastEndpoints;
using Fiestr.Infrastructure.Data.QueryDb;
using Fiestr.UseCases.ProviderReviews;
using FluentValidation;

namespace Fiestr.Web.Endpoints.ProviderReviews.GetByProviderId;

public class Endpoint(QueryDbContext ctx) : Endpoint<Request, List<ProviderReviewDto>>
{
  public override void Configure()
  {
    Get("provider/{providerId:int}/reviews");
    AllowAnonymous();
  }

  public override Task<List<ProviderReviewDto>> ExecuteAsync(Request req, CancellationToken ct)
  {
    var result = ctx.ProviderReviews
      .Where(e => e.ProviderId == req.ProviderId)
      .Select(e => new ProviderReviewDto { ProviderId = e.ProviderId, Id = e.Id, Score = e.Score, Review = e.Review })
      .ToList();

    return Task.FromResult(result);
  }
}

public class Validator : Validator<Request>
{
  public Validator()
  {
    this.RuleFor(e => e.ProviderId)
      .NotEmpty();
  }
}

public record Request(int? ProviderId);
