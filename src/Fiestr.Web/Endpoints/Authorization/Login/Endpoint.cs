using System.Text.Json.Serialization;
using FastEndpoints;
using Fiestr.UseCases.Auth.Login;
using Fiestr.Web.Extensions;
using FluentValidation;
using MediatR;

namespace Fiestr.Web.Endpoints.Authorization.Login;

public class Endpoint(ISender mediator) : Endpoint<Request, Response>
{
  public override void Configure()
  {
    Post("/auth/login");
    AllowAnonymous();
  }

  public override async Task HandleAsync(Request r, CancellationToken ct)
  {
    var command = new LoginCommand(r.Code!, r.RedirectUri!);
    var result = await mediator.Send(command, ct);
    await this.SendResultResponse(result, e => new Response
    {
      Scope = e.Value.Scope,
      AccessToken = e.Value.AccessToken,
      IdToken = e.Value.IdToken,
      ExpiresIn = e.Value.ExpiresIn,
      TokenType = e.Value.TokenType
    });
  }
}

public class Validator : Validator<Request>
{
  public Validator()
  {
    RuleFor(x => x.Code)
      .NotEmpty()
      .MaximumLength(512);
    RuleFor(x => x.RedirectUri)
      .NotEmpty()
      .MaximumLength(512);
  }
}
public record Request(string? Code, string? RedirectUri);
public class Response
{
  [JsonPropertyName("access_token")]
  public required string AccessToken { get; set; }
  
  [JsonPropertyName("id_token")]
  public required string IdToken { get; set; }
  
  [JsonPropertyName("scope")]
  public required string Scope { get; set; }
  
  [JsonPropertyName("expires_in")]
  public required double ExpiresIn { get; set; }
  
  [JsonPropertyName("token_type")]
  public required string TokenType { get; set; }

}

