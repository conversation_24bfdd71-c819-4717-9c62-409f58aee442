// using System.Text.Json.Serialization;
// using FastEndpoints;
// using FluentValidation;
// namespace Fiestr.Web.Endpoints.Authorization.Challenge;
//
// public class Request
// {
//   public string? AccessToken { get; set; }
// }
//
// public class Validator : Validator<Request>
// {
//   public Validator()
//   {
//     this.RuleFor(e => e.AccessToken)
//       .NotEmpty();
//   }
// }
//
// public class Response
// {
//   [JsonPropertyName("access_token")]
//   public required string AccessToken { get; set; }
//   
//   [JsonPropertyName("id_token")]
//   public required string IdToken { get; set; }
//   
//   [JsonPropertyName("scope")]
//   public required string Scope { get; set; }
//   
//   [JsonPropertyName("expires_in")]
//   public required double ExpiresIn { get; set; }
//   
//   [JsonPropertyName("token_type")]
//   public required string TokenType { get; set; }
//
// }
