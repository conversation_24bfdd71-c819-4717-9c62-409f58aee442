// using FastEndpoints;
// using Fiestr.UseCases.Auth.Challenge;
// using Fiestr.UseCases.Auth.Login;
// using Fiestr.Web.Extensions;
// using MediatR;
// namespace Fiestr.Web.Endpoints.Authorization.Challenge;
//
// public class Endpoint(ISender mediator) : Endpoint<Request, Response>
// {
//   public override void Configure()
//   {
//     Post("/auth/challenge");
//     AllowAnonymous();
//   }
//
//   public override async Task HandleAsync(Request r, CancellationToken ct)
//   {
//     var command = new ChallengeCommand(r.AccessToken!);
//     var result = await mediator.Send(command, ct);
//     await this.SendResultResponse(result, e => new Response
//     {
//       Scope = e.Value.Scope,
//       AccessToken = e.Value.AccessToken,
//       IdToken = e.Value.IdToken,
//       ExpiresIn = e.Value.ExpiresIn,
//       TokenType = e.Value.TokenType
//     });
//   }
// }
