using Ardalis.Result;
using FastEndpoints;
using Fiestr.UseCases.Tags;
using Fiestr.UseCases.Tags.List;
using Fiestr.Web.Extensions;
using MediatR;
namespace Fiestr.Web.Endpoints.Tags.Get;

public class Endpoint(ISender mediator) : EndpointWithoutRequest<Result<List<TagDto>>>
{
  public override void Configure()
  {
    Get("/tags");
    ResponseCache(60);
    AllowAnonymous();
  }

  public override async Task HandleAsync(CancellationToken ct)
  {
    var query = new ListTagsQuery();
    var result = await mediator.Send(query, ct);
    await this.SendResultResponse(result, e => e.Value, ct: ct);
  }
}
