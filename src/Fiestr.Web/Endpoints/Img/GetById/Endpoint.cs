using FastEndpoints;
using Fiestr.UseCases.ImageDefinitions;
using Fiestr.UseCases.ImageDefinitions.QueryById;
using Fiestr.Web.Extensions;
using FluentValidation;
using MediatR;
namespace Fiestr.Web.Endpoints.Img.GetById;

public class Endpoint(IImageDefinitionByIdQueryService service) : Endpoint<Request, ImageDefinitionDto>
{
  public override void Configure()
  {
    Get("/img/{id}");
    AllowAnonymous();
    ResponseCache(15, varyByQueryKeys: ["providerId"]);
  }

  public override async Task HandleAsync(Request r, CancellationToken ct)
  {
    var result = await service.GetAsync(r.Id!, ct);
    if (!result.IsSuccess)
    {
      await this.SendResultResponse(result, e => e.Value, ct);
      return;
    }
    var img = result.Value;
    await this.SendBytesAsync(img.Data, contentType: img.ContentType, cancellation: ct);
  }
}

public record Request(string? Id);

public class Validator : Validator<Request>
{
  public Validator()
  {
    this.RuleFor(e => e.Id)
      .NotEmpty();
  }
}
