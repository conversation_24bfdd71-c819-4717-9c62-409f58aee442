// using FastEndpoints;
// using Fiestr.Core.Interfaces;
// namespace Fiestr.Web.Endpoints.Img.Process;
//
// public class Endpoint(IImageService imgService): Endpoint<Request, Response>
// {
//   public override void Configure()
//   {
//     Post("img/process");
//     AllowFileUploads();
//     AllowAnonymous();
//   }
//
//   public override async Task HandleAsync(Request req, CancellationToken ct)
//   {
//     var stream = req.Img.OpenReadStream();
//     if (req.Resize)
//     {
//       stream = await imgService.Resize(stream, req.MaxHeight, ct);
//     }
//     if (req.Compress)
//     {
//       stream = imgService.Compress(stream);
//     }
//     stream.Seek(0, SeekOrigin.Begin);
//     await SendStreamAsync(stream, "hehe.jpeg", contentType: "image/jpeg", cancellation: ct);
//   }
// }
//
// public record Request(IFormFile Img, bool Resize, bool Compress, uint MaxHeight);
// public record Response(int Num);
