// using FastEndpoints;
// using Fiestr.Core.Interfaces;
// using Fiestr.SharedKernel.Extensions;
// namespace Fiestr.Web.Endpoints.Img.Resize;
//
// public class Endpoint(IImageService imgService): Endpoint<Request, Response>
// {
//   public override void Configure()
//   {
//     Post("img/resize");
//     AllowFileUploads();
//     AllowAnonymous();
//   }
//
//   public override async Task HandleAsync(Request req, CancellationToken ct)
//   {
//     var stream = await imgService.Resize(req.Img.OpenReadStream(), 1080, ct);
//     stream.SeekStart();
//     await SendStreamAsync(stream, "hehe.jpeg", contentType: "image/jpeg", cancellation: ct);
//   }
// }
//
// public record Request(IFormFile Img);
// public record Response(int Num);
