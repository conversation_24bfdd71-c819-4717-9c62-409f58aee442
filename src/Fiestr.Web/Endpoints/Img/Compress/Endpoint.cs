// using FastEndpoints;
// using Fiestr.Core.Interfaces;
// using Fiestr.Web.Extensions;
// namespace Fiestr.Web.Endpoints.Img.Compress;
//
// public class Endpoint(IImageService imgService): Endpoint<Request, Response>
// {
//   public override void Configure()
//   {
//     Post("img/compress");
//     AllowFileUploads();
//     AllowAnonymous();
//   }
//
//   public override async Task HandleAsync(Request req, CancellationToken ct)
//   {
//     var stream = imgService.Compress(req.Img.OpenReadStream());
//     stream.Seek(0, SeekOrigin.Begin);
//     await SendStreamAsync(stream, "hehe.jpeg", contentType: "image/jpeg", cancellation: ct);
//   }
// }
//
// public record Request(IFormFile Img);
// public record Response(int Num);
