// using FastEndpoints;
// using Fiestr.Core.Interfaces;
// using Fiestr.UseCases.Auth;
// using Fiestr.UseCases.Users.GetById;
// using Fiestr.Web.Extensions;
// using MediatR;
//
// namespace Fiestr.Web.Endpoints.Users.Me;
//
// public class Endpoint(ISender mediator, IIdentityUser user): EndpointWithoutRequest<UserDto>
// {
//   public override void Configure()
//   {
//     Get("/users/me");
//   }
//
//   public override async Task HandleAsync(CancellationToken ct)
//   {
//     var q = new UserByIdQuery(user.Id);
//     var result = await mediator.Send(q, ct);
//     await this.SendResultResponse(result, e => e.Value, ct);
//   }
// }
