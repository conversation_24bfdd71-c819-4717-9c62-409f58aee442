// using FastEndpoints;
// using Fiestr.Core.Enums;
// using Fiestr.Infrastructure.Data.Config;
// using Fiestr.UseCases.Products.Create;
// using Fiestr.Web.Extensions;
// using FluentValidation;
// using MediatR;
// namespace Fiestr.Web.Endpoints.Products.Create;
//
// public class Endpoint(ISender mediator) : Endpoint<Request>
// {
//   public override void Configure()
//   {
//     Post("/products");
//   }
//
//   public override async Task HandleAsync(Request r, CancellationToken ct)
//   {
//       var command = new CreateProductCommand
//       {
//         Name = r.Name ?? string.Empty,
//         ProviderId = r.ProviderId ?? 0,
//         Description = r.Description ?? string.Empty,
//         LongDescription = r.LongDescription,
//         Sku = r.Sku,
//         UnitType = r.UnitType!.Value,
//         UnitPrice = r.UnitPrice!.Value
//       };
//      var result = await mediator.Send(command, ct);
//      if (result.IsSuccess)
//      {
//        await this.SendCreatedAtAsync<GetById.Endpoint>(new
//        {
//          Id = result.Value
//        }, new Response(), cancellation: ct);
//      }
//      else
//      {
//        await this.SendResultResponse(result, e => e, ct: ct);
//      }
//   }
// }
//
// public record Request
// {
//   public int? ProviderId { get; set; }
//   public string? Name { get; set; }
//   public string? Description { get; set; }
//   public string? LongDescription { get; private set; }
//   public string? Sku { get; private set; }
//   public UnitType? UnitType { get; private set; } 
//   public decimal? UnitPrice { get; private set; }
// }
//
// public class Validator : Validator<Request>
// {
//   public Validator()
//   {
//     RuleFor(r => r.ProviderId).NotNull();
//     RuleFor(r => r.Name).NotNull().NotEmpty().MaximumLength(DataSchemaConstants.DEFAULT_NAME_LENGTH);
//     RuleFor(r => r.Description).NotNull().NotEmpty().MaximumLength(DataSchemaConstants.DEFAULT_DESCRIPTION_LENGTH);
//     RuleFor(r => r.LongDescription).MaximumLength(DataSchemaConstants.DEFAULT_DESCRIPTION_LENGTH);
//     RuleFor(r => r.UnitType).NotNull().IsInEnum();
//     RuleFor(r => r.UnitPrice).NotNull().GreaterThanOrEqualTo(0);
//   }
// }
// public record Response;
//
