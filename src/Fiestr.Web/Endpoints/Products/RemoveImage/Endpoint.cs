// using FastEndpoints;
// using Fiestr.Infrastructure.ImageProcessing;
// using Fiestr.UseCases.Products.AddImage;
// using Fiestr.UseCases.Products.RemoveImage;
// using Fiestr.Web.Extensions;
// using Fiestr.Web.Validators;
// using FluentValidation;
// using MediatR;
// using Microsoft.Extensions.Options;
// namespace Fiestr.Web.Endpoints.Products.RemoveImage;
//
// public class Endpoint(ISender sender): Endpoint<Request>
// {
//   public override void Configure()
//   {
//     Delete("/products/{id}/image/{imgId}");
//   }
//   public override async  Task HandleAsync(Request req, CancellationToken ct)
//   {
//     var command = new RemoveProductImageCommand(req.Id, req.ImgId);
//     var result = await sender.Send(command, ct);
//     await this.SendResultResponse(result, e => e.Value, ct);
//   }
// }
//
// public record Request(int Id, string ImgId);
//
// public class Validator: Validator<Request>
// {
//   public Validator()
//   {
//     this.RuleFor(e => e.Id)
//       .NotEmpty();
//
//     RuleFor(x => x.ImgId)
//       .NotEmpty();
//   }
// }
