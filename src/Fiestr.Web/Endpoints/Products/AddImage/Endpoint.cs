// using FastEndpoints;
// using Fiestr.Infrastructure.ImageProcessing;
// using Fiestr.UseCases.Products.AddImage;
// using Fiestr.Web.Extensions;
// using Fiestr.Web.Validators;
// using FluentValidation;
// using MediatR;
// using Microsoft.Extensions.Options;
// namespace Fiestr.Web.Endpoints.Products.AddImage;
//
// public class Endpoint(ISender sender): Endpoint<Request>
// {
//   public override void Configure()
//   {
//     Post("products/{id}/image");
//     AllowFileUploads();
//   }
//   
//   public override async Task HandleAsync(Request req, CancellationToken ct)
//   {
//     var command = new AddProductImageCommand(req.Id, req.Image!.OpenReadStream());
//     var result = await sender.Send(command, ct);
//     await this.SendResultResponse(result, e => e.Value, ct);
//   }
// }
//
// public record Request(int Id, IFormFile? Image);
//
// public class Validator: Validator<Request>
// {
//   public Validator(IOptions<ImageConfiguration> imgCfg)
//   {
//     this.RuleFor(e => e.Id)
//       .NotEmpty();
//     
//     RuleFor(x => x.Image)
//       .NotEmpty()
//       .ImageSizeValidator(imgCfg.Value.MinBytes, imgCfg.Value.MaxBytes)
//       .ImageFormatValidator(imgCfg.Value.SupportedFormats);
//   }
// }
