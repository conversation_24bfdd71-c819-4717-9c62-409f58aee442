using FastEndpoints;
using Fiestr.UseCases.Products;

namespace Fiestr.Web.Endpoints.Products.ListProviderProducts;

public class Endpoint(IListProductsForProviderQueryService service) : Endpoint<Request, List<ProductDto>>
{
  public override void Configure()
  {
    AllowAnonymous();
    Get("/providers/{providerId}/products");
    ResponseCache(15, varyByQueryKeys: ["providerId"]);
  }

  public override async Task HandleAsync(Request r, CancellationToken ct)
  {
    var result = service.List(r.ProviderId);
    await this.SendAsync(result, cancellation: ct);
  }
}

public record Request(int ProviderId);

