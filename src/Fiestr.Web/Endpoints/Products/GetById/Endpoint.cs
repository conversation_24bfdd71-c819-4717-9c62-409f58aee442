using FastEndpoints;
using Fiestr.UseCases.Products;
using Fiestr.UseCases.Products.Create;
using Fiestr.UseCases.Products.GetById;
using Fiestr.Web.Extensions;
using FluentValidation;
using MediatR;
namespace Fiestr.Web.Endpoints.Products.GetById;

public class Endpoint(ISender mediatr) : Endpoint<Request, ProductDto>
{
  public override void Configure()
  {
    Get("/products/{id}");
    AllowAnonymous();
  }

  public override async Task HandleAsync(Request r, CancellationToken ct)
  {
    var command = new ProductByIdQuery(r.Id);
    var result = await mediatr.Send(command, ct);
    await this.SendResultResponse(result, e => e.Value, ct);
  }
}

public record Request(int Id);

