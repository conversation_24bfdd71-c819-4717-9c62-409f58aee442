using System.Reflection;
using System.Text;
using System.Text.Json.Serialization;
using Ardalis.GuardClauses;
using Ardalis.ListStartupServices;
using Ardalis.SharedKernel;
using FastEndpoints;
using FastEndpoints.Swagger;
using Fiestr.Core.Aggregates.ProviderAggregate;
using Fiestr.Core.Interfaces;
using Fiestr.Infrastructure;
using Fiestr.Infrastructure.Data;
using Fiestr.Infrastructure.Email;
using Fiestr.Infrastructure.Security;
using Fiestr.SharedKernel;
using Fiestr.UseCases.Providers.Create;
using Fiestr.Web;
using Fiestr.Web.Auth;
using Fiestr.Web.Middleware;
using FluentValidation;
using MediatR;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using Serilog;
using Serilog.Extensions.Logging;
var logger = Log.Logger = new LoggerConfiguration()
  .Enrich.FromLogContext()
  .CreateLogger();

logger.Information("Starting web host");

var builder = WebApplication.CreateBuilder(args);

// Configure Kestrel only for Development environment
// Production configuration is handled via appsettings.Production.json and environment variables
if (builder.Environment.IsDevelopment())
{
  builder.WebHost.ConfigureKestrel(options =>
  {
    options.ListenAnyIP(5000); // HTTP
    options.ListenAnyIP(5001, listenOptions =>
    {
      listenOptions.UseHttps(); // HTTPS
    });
  });
}


builder.Services.AddCors(options =>
{
  options.AddPolicy("AllowAllOrigins",
    policyBuilder =>
    {
      policyBuilder.AllowAnyOrigin()
        .AllowAnyMethod()
        .AllowAnyHeader();
    });
});

// LOGGING
builder.Host.UseSerilog((_, config) => config.ReadFrom.Configuration(builder.Configuration));

// IDENTITY------------------------------------------------------------------------------------------------
var identityCfg = builder.Configuration.GetSection(FiestrJwtConfiguration.Location).Get<FiestrJwtConfiguration>();
builder.Services.Configure<FiestrJwtConfiguration>(builder.Configuration.GetSection(FiestrJwtConfiguration.Location));
Guard.Against.Null(identityCfg);
builder.Services.AddAuthentication(options =>
  {
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
  })
  .AddJwtBearer(opt =>
  {
    opt.TokenValidationParameters = new TokenValidationParameters
    {
      ValidateIssuerSigningKey = true,
      IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(identityCfg.Key)),
      ValidateIssuer = true,
      ValidIssuer = identityCfg.Issuer,
      ValidateAudience = true,
      ValidAudience = identityCfg.Audience,
      ValidateLifetime = true
    };
  });
builder.Services.AddAuthorization();
builder.Services.AddHttpContextAccessor();
builder.Services.AddTransient<IIdentityUser, IdentityUser>();
Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.DefaultInboundClaimTypeMap.Clear();
builder.Services.AddScoped<AddCustomClaimsMiddleware>();
// --------------------------------------------------------------------------------------------------------

var microsoftLogger = new SerilogLoggerFactory(logger)
    .CreateLogger<Fiestr.Web.Program>();

// Configure Web Behavior
builder.Services.Configure<CookiePolicyOptions>(options =>
{
  options.CheckConsentNeeded = context => true;
  options.MinimumSameSitePolicy = SameSiteMode.None;
});

builder.Services.AddFastEndpoints()
  .SwaggerDocument(o =>
  {
    o.ShortSchemaNames = true;
  })
  .AddResponseCaching();

ConfigureMediatR();

builder.Services.AddInfrastructureServices(builder.Configuration, microsoftLogger);

if (builder.Environment.IsDevelopment())
{
  // Use a local test email server
  // See: https://ardalis.com/configuring-a-local-test-email-server/
  builder.Services.AddScoped<IEmailSender, MimeKitEmailSender>();

  // Otherwise use this:
  //builder.Services.AddScoped<IEmailSender, FakeEmailSender>();
  AddShowAllServicesSupport();
}
else
{
  builder.Services.AddScoped<IEmailSender, MimeKitEmailSender>();
}






// APP ----------------
var app = builder.Build();

if (app.Environment.IsDevelopment())
{
  app.UseDeveloperExceptionPage();
  app.UseShowAllServicesMiddleware(); // see https://github.com/ardalis/AspNetCoreStartupServices
}
else
{
  app.UseDefaultExceptionHandler(); // from FastEndpoints
  app.UseHsts();
}

app.UseHttpsRedirection();

// CORS----------------------------------------------------------------------------------------------------
app.UseCors("AllowAllOrigins");
app.UseAuthentication();
app.UseAuthorization();
app.UseMiddleware<AddCustomClaimsMiddleware>();
app
  .UseResponseCaching()
  .UseFastEndpoints(c =>
  {
    c.Endpoints.RoutePrefix = "api";
    c.Serializer.Options.Converters.Add(new JsonStringEnumConverter());

  })
    .UseSwaggerGen(); // Includes AddFileServer and static files middleware

SeedDatabase(app);

app.Run();
// APP ----------------


static void SeedDatabase(WebApplication app)
{
  using var scope = app.Services.CreateScope();
  var services = scope.ServiceProvider;

  try
  {
    var context = services.GetRequiredService<AppDbContext>();
    //          context.Database.Migrate();
    context.Database.EnsureCreated();
    // SeedData.Initialize(services);
  }
  catch (Exception ex)
  {
    var logger = services.GetRequiredService<ILogger<Fiestr.Web.Program>>();
    logger.LogError(ex, "An error occurred seeding the DB. {exceptionMessage}", ex.Message);
  }
}

void ConfigureMediatR()
{
  var mediatRAssemblies = new[]
  {
    Assembly.GetAssembly(typeof(Provider)), // Core
    Assembly.GetAssembly(typeof(CreateProviderCommand)) // UseCases
  };
  builder.Services.AddValidatorsFromAssembly(Assembly.GetAssembly(typeof(CreateProviderCommand)));
  builder.Services.AddMediatR(cfg => cfg.RegisterServicesFromAssemblies(mediatRAssemblies!));
  builder.Services.AddScoped(typeof(IPipelineBehavior<,>), typeof(LoggingBehavior<,>));
  builder.Services.AddScoped(typeof(IPipelineBehavior<,>), typeof(ValidationBehavior<,>));
  builder.Services.AddScoped<IDomainEventDispatcher, MediatRDomainEventDispatcher>();
}

void AddShowAllServicesSupport()
{
  // add list services for diagnostic purposes - see https://github.com/ardalis/AspNetCoreStartupServices
  builder.Services.Configure<ServiceConfig>(config =>
  {
    config.Services = new List<ServiceDescriptor>(builder.Services);

    // optional - default path to view services is /listallservices - recommended to choose your own path
    config.Path = "/listservices";
  });
}


// Make the implicit Program.cs class public, so integration tests can reference the correct assembly for host building
namespace Fiestr.Web
{
  public partial class Program
  {
  }
}
