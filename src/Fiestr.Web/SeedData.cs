// using Dapper;
// using Fiestr.Core.Aggregates.ProviderAggregate;
// using Fiestr.Core.Aggregates.ProductAggregate;
// using Fiestr.Core.Aggregates.ProviderReviewAggregate;
// using Fiestr.Core.Enums;
// using Fiestr.Infrastructure.Data;
// using Microsoft.EntityFrameworkCore;
//
// namespace Fiestr.Web;
//
// public static class SeedData
// {
//   public static void Initialize(IServiceProvider serviceProvider)
//   {
//     using (var dbContext = new AppDbContext(
//         serviceProvider.GetRequiredService<DbContextOptions<AppDbContext>>(), null))
//     {
//       // PopulateTestData(dbContext);
//     }
//   }
//
//   public static void PopulateTestData(AppDbContext dbContext)
//   {
//     InitializeTags(dbContext);
//     InitializeProviders(dbContext);
//     InitializeProviderReviews(dbContext);
//     InitializeProducts(dbContext);
//     InitializeProviderTags(dbContext);
//   }
//
//   private static void InitializeProviders(AppDbContext dbContext)
//   {
//     dbContext.Database.GetDbConnection().Execute("DELETE FROM Provider; DELETE FROM sqlite_sequence WHERE name='Provider';");
//
//     // Insert providers into the database
//     dbContext.Database.GetDbConnection().Execute(
//       "INSERT INTO Provider (Name, PhoneNumber, Address1, CreatedAt) VALUES (@Name, @PhoneNumber, @Address1, @CreatedAt)",
//       Providers.Select(e => new
//       {
//         e.Name,
//         e.PhoneNumber,
//         e.Address1,
//         e.CreatedAt
//       }));
//
//     // Retrieve the generated Provider IDs and update the Providers list
//     var providersFromDb = dbContext.Providers.ToList();
//
//     foreach (var provider in Providers)
//     {
//       var matchingProvider = providersFromDb.FirstOrDefault(p => p.Name == provider.Name);
//       if (matchingProvider != null)
//       {
//         provider.Id = matchingProvider.Id;
//       }
//     }
//   }
//   
//   private static void InitializeProviderReviews(AppDbContext dbContext)
//   {
//     dbContext.Database.GetDbConnection().Execute("DELETE FROM ProviderReview; DELETE FROM sqlite_sequence WHERE name='ProviderReview';");
//
//     // Insert reviews into the database
//     dbContext.Database.GetDbConnection().Execute(
//       "INSERT INTO ProviderReview (ProviderId, Score, Review) VALUES (@ProviderId, @Score, @Review)",
//       ProviderReviews.Select(e => new
//       {
//         e.ProviderId,
//         e.Score,
//         e.Review
//       }));
//
//     // Retrieve the generated Provider IDs and update the Providers list
//     var providersFromDb = dbContext.Providers.ToList();
//
//     foreach (var provider in Providers)
//     {
//       var matchingProvider = providersFromDb.FirstOrDefault(p => p.Name == provider.Name);
//       if (matchingProvider != null)
//       {
//         provider.Id = matchingProvider.Id;
//       }
//     }
//   }
//   
//   private static void InitializeTags(AppDbContext dbContext)
//   {
//     dbContext.Database.GetDbConnection().Execute("DELETE FROM Tags; DELETE FROM sqlite_sequence WHERE name='Tags';");
//
//     // Insert tags into the database
//     dbContext.Database.GetDbConnection().Execute(
//       "INSERT INTO Tags (Name, Category, Code, EntityType) VALUES (@Name, @Category, @Code, @EntityType)",
//       Tags.Select(e => new
//       {
//         e.Name,
//         e.Category,
//         e.Code,
//         EntityType = (long)e.EntityType
//       }));
//
//     // Retrieve the generated Tag IDs and update the Tags list
//     var tagsFromDb = dbContext.Tags.ToList();
//
//     foreach (var tag in Tags)
//     {
//       var matchingTag = tagsFromDb.FirstOrDefault(t => t.Name == tag.Name && t.Category == tag.Category);
//       if (matchingTag != null)
//       {
//         tag.Id = matchingTag.Id;
//       }
//     }
//   }
//
//   private static void InitializeProducts(AppDbContext dbContext)
//   {
//     dbContext.Database.GetDbConnection().Execute("delete from Product; delete from sqlite_sequence where name='Products';");
//
//     dbContext.Database.GetDbConnection().Execute(
//         "INSERT INTO Product (ProviderId, Name, Description, LongDescription, UnitType, UnitPrice) VALUES (@ProviderId, @Name, @Description, @LongDescription, @UnitType, @UnitPrice)",
//         Products.Select(e => new
//         {
//           e.ProviderId,
//           e.Name,
//           e.Description,
//           e.LongDescription,
//           UnitType = (int)e.UnitType,
//           e.UnitPrice
//         }));
//   }
//   
//   private static void InitializeProviderTags(AppDbContext dbContext)
//   {
//     // Delete existing ProviderTags entries
//     dbContext.Database.GetDbConnection().Execute("DELETE FROM ProviderTags;");
//
//     // Retrieve the list of all tags from the database
//     var tagsFromDb = dbContext.Tags.ToList();
//
//     // Build a list to hold the provider-tag relationships
//     var providerTags = new List<dynamic>();
//
//     foreach (var provider in Providers)
//     {
//       if (ProviderTagMappings.TryGetValue(provider.Name, out var tagCodes))
//       {
//         foreach (var tagCode in tagCodes)
//         {
//           var tag = tagsFromDb.FirstOrDefault(t => t.Code == tagCode);
//           if (tag != null)
//           {
//             providerTags.Add(new { ProviderId = provider.Id, TagId = tag.Id });
//           }
//         }
//       }
//     }
//
//     // Insert ProviderTags into the database
//     dbContext.Database.GetDbConnection().Execute(
//       "INSERT INTO ProviderTags (ProviderId, TagId) VALUES (@ProviderId, @TagId)", providerTags);
//   }
//
//   public static List<Provider> Providers = new()
//   {
//     new Provider("La Casa del Banquete", "**********", "Avenida Reforma 123"),
//     new Provider("Eventos y Más", "**********", "Calle Principal 456"),
//     new Provider("Banquetes Gourmet", "**********", "Boulevard del Sol 789"),
//     new Provider("Fiestas y Diversión", "**********", "Calle Alegría 321"),
//     new Provider("Eventos Especiales", "**********", "Avenida Central 654"),
//   };
//
//   public static List<ProviderReview> ProviderReviews = new()
//   {
//     new ProviderReview(1, 20, "Buen servicio"),
//     new ProviderReview(2, 15, "Excelente atención al cliente"),
//     new ProviderReview(3, 85, "Servicio aceptable"),
//     new ProviderReview(1, 25, "Rápido y eficiente"),
//     new ProviderReview(2, 30, "Atento y respetuoso"),
//     new ProviderReview(3, 30, "Buen seguimiento"),
//     new ProviderReview(4, 35, "Excepcional en su trabajo"),
//     new ProviderReview(1, 40, "Comunicación clara"),
//     new ProviderReview(2, 45, "Soporte de calidad"),
//     new ProviderReview(3, 50, "Se adapta a las necesidades del cliente"),
//     new ProviderReview(4, 55, "Altamente recomendado"),
//     new ProviderReview(1, 60, "Experiencia positiva en general"),
//     new ProviderReview(2, 65, "Competente y profesional"),
//     new ProviderReview(3, 60, "Tiempo de respuesta rápido"),
//     new ProviderReview(4, 55, "Comprendió y resolvió mis problemas"),
//     new ProviderReview(1, 50, "Gran nivel de habilidad"),
//     new ProviderReview(2, 45, "Regresaría para futuros trabajos"),
//     new ProviderReview(3, 40, "Se esfuerza por superar las expectativas"),
//     new ProviderReview(4, 40, "Experiencia de servicio excepcional"),
//   };  
//   
//   public static List<Tag> Tags =
//   [
//     new Tag(TagEntityType.Provider, "State", "State|Aguascalientes", "Aguascalientes"),
//     new Tag(TagEntityType.Provider, "State", "State|BajaCalifornia", "Baja California"),
//     new Tag(TagEntityType.Provider, "State", "State|BajaCaliforniaSur", "Baja California Sur"),
//     new Tag(TagEntityType.Provider, "State", "State|Campeche", "Campeche"),
//     new Tag(TagEntityType.Provider, "State", "State|Chiapas", "Chiapas"),
//     new Tag(TagEntityType.Provider, "State", "State|Chihuahua", "Chihuahua"),
//     new Tag(TagEntityType.Provider, "State", "State|CDMX", "CDMX"),
//     new Tag(TagEntityType.Provider, "State", "State|Coahuila", "Coahuila"),
//     new Tag(TagEntityType.Provider, "State", "State|Colima", "Colima"),
//     new Tag(TagEntityType.Provider, "State", "State|Durango", "Durango"),
//     new Tag(TagEntityType.Provider, "State", "State|EstadodeMexico", "Estado de Mexico"),
//     new Tag(TagEntityType.Provider, "State", "State|Guanajuato", "Guanajuato"),
//     new Tag(TagEntityType.Provider, "State", "State|Guerrero", "Guerrero"),
//     new Tag(TagEntityType.Provider, "State", "State|Hidalgo", "Hidalgo"),
//     new Tag(TagEntityType.Provider, "State", "State|Jalisco", "Jalisco"),
//     new Tag(TagEntityType.Provider, "State", "State|Michoacan", "Michoacán"),
//     new Tag(TagEntityType.Provider, "State", "State|Morelos", "Morelos"),
//     new Tag(TagEntityType.Provider, "State", "State|Nayarit", "Nayarit"),
//     new Tag(TagEntityType.Provider, "State", "State|NuevoLeon", "Nuevo León"),
//     new Tag(TagEntityType.Provider, "State", "State|Oaxaca", "Oaxaca"),
//     new Tag(TagEntityType.Provider, "State", "State|Puebla", "Puebla"),
//     new Tag(TagEntityType.Provider, "State", "State|Queretaro", "Querétaro"),
//     new Tag(TagEntityType.Provider, "State", "State|QuintanaRoo", "Quintana Roo"),
//     new Tag(TagEntityType.Provider, "State", "State|SanLuisPotosi", "San Luis Potosí"),
//     new Tag(TagEntityType.Provider, "State", "State|Sinaloa", "Sinaloa"),
//     new Tag(TagEntityType.Provider, "State", "State|Sonora", "Sonora"),
//     new Tag(TagEntityType.Provider, "State", "State|Tabasco", "Tabasco"),
//     new Tag(TagEntityType.Provider, "State", "State|Tamaulipas", "Tamaulipas"),
//     new Tag(TagEntityType.Provider, "State", "State|Tlaxcala", "Tlaxcala"),
//     new Tag(TagEntityType.Provider, "State", "State|Veracruz", "Veracruz"),
//     new Tag(TagEntityType.Provider, "State", "State|Yucatan", "Yucatán"),
//     new Tag(TagEntityType.Provider, "State", "State|Zacatecas", "Zacatecas"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Lugares|Salones", "Salones"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Lugares|Jardines", "Jardines"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Lugares|Terrazas", "Terrazas"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Lugares|CentrosDeConvenciones", "Centros de convenciones"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Lugares|Haciendas", "Haciendas"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Lugares|Vinedos", "Viñedos"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Lugares|Hoteles", "Hoteles"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Lugares|Roofs", "Roofs"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Lugares|Iglesias", "Iglesias"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Coordinacion|PlaneadorDeEvento", "Planeador de Evento"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Coordinacion|PlaneadorDeBodas", "Planeador de Bodas"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Coordinacion|Coordinador", "Coordinador"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Coordinacion|AsesorProtocolo", "Asesor Protocolo"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Coordinacion|Supervision", "Supervisión"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Coordinacion|Cronograma", "Cronograma"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Coordinacion|Asesoria", "Asesoría"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Banquete|Buffet", "Buffet"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Banquete|MenuDeAutor", "Menú de autor"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Banquete|CateringPorTiempos", "Catering por tiempos"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Banquete|ReposteriaFina", "Repostería fina"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Banquete|Tacos", "Tacos"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Banquete|Snacks", "Snacks"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Banquete|AntojitosMexicanos", "Antojitos Mexicanos"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Banquete|MenuEspecialidad", "Menú especialidad"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Banquete|ComidaRapida", "Comida rápida"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Banquete|FoodTrucks", "Food Trucks"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Bebidas|Cocteleria", "Coctelería"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Bebidas|Descorche", "Descorche"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Bebidas|Hielos", "Hielos"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Bebidas|Refrescos", "Refrescos"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Bebidas|AguasFrescas", "Aguas frescas"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Bebidas|Catas", "Catas"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Bebidas|BarraLibre", "Barra Libre"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Meseros|Servicio", "Servicio"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Meseros|CapitanDeMeseros", "Capitán de Meseros"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Meseros|CoordinacionDeMesa", "Coordinación de mesa"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Meseros|Montaje", "Montaje"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Meseros|Desmontaje", "Desmontaje"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Meseros|Barman", "Barman"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Decoracion|Tematica", "Temática"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Decoracion|Arreglos", "Arreglos"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Decoracion|CentrosDeMesa", "Centros de mesa"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Decoracion|DecoracionFloral", "Decoración floral"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Decoracion|DecoracionAerea", "Decoración Aérea"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Decoracion|AccesoriosDecorativos", "Accesorios decorativos"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Decoracion|Globos", "Globos"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Decoracion|DecoracionReligiosa", "Decoración Religiosa"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Decoracion|DecoracionCivil", "Decoración Civil"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Mobiliario|Mesas", "Mesas"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Mobiliario|Sillas", "Sillas"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Mobiliario|Accesorios", "Accesorios"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Mobiliario|Lounge", "Lounge"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Mobiliario|Sofas", "Sofás"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Mobiliario|Carpas", "Carpas"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Mobiliario|Sombrillas", "Sombrillas"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Mobiliario|Calentadores", "Calentadores"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Mobiliario|Velas", "Velas"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|MontajeDeMesa|Cubiertos", "Cubiertos"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|MontajeDeMesa|BajoPlatos", "Bajo platos"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|MontajeDeMesa|Manteles", "Manteles"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|MontajeDeMesa|Vajillas", "Vajillas"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|MontajeDeMesa|Cristaleria", "Cristalería"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|MontajeDeMesa|CaminosDeMesa", "Caminos de mesa"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|MontajeDeMesa|NumerosDeMesa", "Números de mesa"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Floreria|Arreglos", "Arreglos"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Floreria|Ramos", "Ramos"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Floreria|CentrosDeMesa", "Centros de mesa"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Floreria|FloresFrescas", "Flores frescas"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Floreria|DecoracionFloral", "Decoración floral"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Floreria|Bouquets", "Bouquets"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Iluminacion|Fijos", "Fijos"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Iluminacion|Efectos", "Efectos"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Iluminacion|Portatiles", "Portátiles"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Iluminacion|LucesLed", "Luces LED"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Iluminacion|Proyectores", "Proyectores"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Iluminacion|Candelabros", "Candelabros"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Iluminacion|VelasLed", "Velas LED"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Audiovisual|Audio", "Audio"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Audiovisual|Microfonos", "Micrófonos"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Audiovisual|Altavoces", "Altavoces"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Audiovisual|EquiposDeSonido", "Equipos de sonido"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Audiovisual|SistemasDeAudio", "Sistemas de audio"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Audiovisual|Monitores", "Monitores"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Audiovisual|Pantallas", "Pantallas"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Audiovisual|Proyectores", "Proyectores"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Musica|Banda", "Banda"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Musica|Dj", "DJ"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Musica|Playlist", "Playlist"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Musica|MusicaEnVivo", "Música en vivo"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Musica|Actuaciones", "Actuaciones"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Musica|GruposMusicales", "Grupos musicales"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Musica|Mariachi", "Mariachi"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Musica|Norteno", "Norteño"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Fotografia|Retrato", "Retrato"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Fotografia|Video", "Video"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Fotografia|Album", "Álbum"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Fotografia|Fotobooth", "Fotobooth"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Fotografia|Cobertura", "Cobertura"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Fotografia|ImpresionInmediata", "Impresión inmediata"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Fotografia|FotoAerea", "Foto aérea"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Fotografia|Pasteles", "Pasteles"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Fotografia|Postres", "Postres"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Fotografia|Dulces", "Dulces"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Fotografia|Tartas", "Tartas"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Fotografia|Cupcakes", "Cupcakes"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Fotografia|Galletas", "Galletas"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Fotografia|MesaDePostres", "Mesa de postres"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Invitaciones|Diseno", "Diseño"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Invitaciones|Impresion", "Impresión"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Invitaciones|InvitacionesDigitales", "Invitaciones digitales"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Invitaciones|TarjetasDeAgradecimiento", "Tarjetas de agradecimiento"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Invitaciones|SaveTheDate", "Save the date"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Vestuario|Trajes", "Trajes"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Vestuario|Vestidos", "Vestidos"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Vestuario|Accesorios", "Accesorios"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Vestuario|AtuendosParaElEvento", "Atuendos para el evento"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Vestuario|Calzado", "Calzado"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Vestuario|VestimentaTematica", "Vestimenta temática"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Transporte|Autos", "Autos"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Transporte|Autobuses", "Autobuses"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Transporte|Limusinas", "Limusinas"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Transporte|TransportePrivado", "Transporte privado"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Transporte|RentaDeVehiculos", "Renta de vehículos"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Transporte|Shuttles", "Shuttles"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Entretenimiento|Shows", "Shows"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Entretenimiento|Juegos", "Juegos"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Entretenimiento|Actividades", "Actividades"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Entretenimiento|Performances", "Performances"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Entretenimiento|Magos", "Magos"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Entretenimiento|Animadores", "Animadores"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Entretenimiento|Artistas", "Artistas"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Entretenimiento|JuegosParaNinos", "Juegos para niños"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Entretenimiento|Nineras", "Niñeras"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Recuerdos|Souvenirs", "Souvenirs"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Recuerdos|Detalles", "Detalles"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Recuerdos|ArticulosPersonalizados", "Artículos personalizados"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Recuerdos|KitsDeAgradecimiento", "Kits de agradecimiento"),
//     new Tag(TagEntityType.Provider, "Provider", "Provider|Recuerdos|FotografiasDeRecuerdo", "Fotografías de recuerdo"),
//   ];
//
//   public static List<Product> Products = new()
//   {
//       // Loza Semicuadrada
//       new Product(1, "Plato Trinche SC", "Plato Trinche Semicuadrada", "Loza Semicuadrada", UnitType.Unit, 3.00m),
//       new Product(1, "Plato Postre SC", "Plato Postre Semicuadrada", "Loza Semicuadrada", UnitType.Unit, 3.00m),
//       new Product(1, "Plato Sopero SC", "Plato Sopero Semicuadrada", "Loza Semicuadrada", UnitType.Unit, 3.00m),
//       new Product(1, "Plato Café SC", "Platito Thermo Semicuadrada", "Loza Semicuadrada", UnitType.Unit, 3.00m),
//       new Product(1, "Taza Café SC", "Taza Thermo Semicuadrada", "Loza Semicuadrada", UnitType.Unit, 3.00m),
//
//       // Loza Redonda
//       new Product(2, "Plato Tazón Manhattan Negro", "Plato Tazón Negro Redondo", "Loza Redonda", UnitType.Unit, 5.00m),
//       new Product(2, "Plato Trinche Manhattan Negro", "Plato Trinche Negro Redondo", "Loza Redonda", UnitType.Unit, 5.00m),
//       new Product(2, "Plato Trinche Blanco R", "Plato Trinche Redondo", "Loza Redonda", UnitType.Unit, 3.00m),
//       new Product(2, "Plato Postre Blanco R", "Plato Postre Redondo", "Loza Redonda", UnitType.Unit, 3.00m),
//       new Product(2, "Plato Sopero Blanco R", "Plato Sopero Redondo", "Loza Redonda", UnitType.Unit, 3.00m),
//       new Product(2, "Plato Pan Blanco R", "Plato Pan Redondo", "Loza Redonda", UnitType.Unit, 3.00m),
//       new Product(2, "Plato Pasta Blanco R", "Plato Pasta Redondo", "Loza Redonda", UnitType.Unit, 4.50m),
//       new Product(2, "Taza Café Blanco R", "Taza Thermo Redonda", "Loza Redonda", UnitType.Unit, 3.00m),
//       new Product(2, "Plato Café Blanco R", "Platito Thermo Redonda", "Loza Redonda", UnitType.Unit, 3.00m),
//
//       // Decorativos
//       new Product(3, "Plato Base Blanco Cuadrado", "Plato Base Blanco Cuadrado Madera", "Decorativo Cuadrado", UnitType.Unit, 8.00m),
//       new Product(3, "Plato Base Dorado Cuadrado", "Plato Base Dorado Cuadrado Resina", "Decorativo Cuadrado", UnitType.Unit, 11.00m),
//       new Product(3, "Plato Base Dorado Redondo", "Plato Base Dorado Redondo Resina", "Decorativo Redondo", UnitType.Unit, 11.00m),
//       new Product(3, "Plato Base Plata Cuadrado", "Plato Base Plata Cuadrado Resina", "Decorativo Cuadrado", UnitType.Unit, 11.00m),
//       new Product(3, "Plato Base Rosa Redondo", "Plato Base Rosa Redondo Resina", "Decorativo Redondo", UnitType.Unit, 12.00m),
//       new Product(3, "Plato Base Blanco Rivoli", "Plato Base Blanco Redondo Resina", "Decorativo Redondo", UnitType.Unit, 11.00m),
//       new Product(3, "Plato Base Negro Redondo", "Plato Base Negro Redondo Resina", "Decorativo Redondo", UnitType.Unit, 12.00m),
//       new Product(3, "Plato Base Rojo Cuadrado", "Plato Base Rojo Cuadrado Resina", "Decorativo Cuadrado", UnitType.Unit, 11.00m),
//       new Product(3, "Plato Base Champán Redondo", "Plato Base Champán Redondo Resina", "Decorativo Redondo", UnitType.Unit, 12.00m),
//       new Product(3, "Plato Base Transparente/Dorado Redondo", "Plato Base Transparente con puntos dorados redondo", "Decorativo Redondo", UnitType.Unit, 13.00m),
//       new Product(3, "Plato Base Oro Bellagio", "Plato Base Oro Bellagio Redondo", "Decorativo Redondo", UnitType.Unit, 12.00m),
//       new Product(3, "Plato Base Plata Bellagio", "Plato Base Plata Bellagio Redondo", "Decorativo Redondo", UnitType.Unit, 12.00m),
//       new Product(3, "Plato Base Mimbre", "Plato Base Mimbre Plata Redondo", "Decorativo Redondo", UnitType.Unit, 13.00m),
//       new Product(3, "Plato Base Splash Oro", "Plato Base Splash Redondo Oro", "Decorativo Redondo", UnitType.Unit, 12.00m),
//       new Product(3, "Plato Base Splash Rosa", "Plato Base Splash Redondo Rosa", "Decorativo Redondo", UnitType.Unit, 12.00m),
//       new Product(3, "Plato Base Splash Negro", "Plato Base Splash Redondo Negro", "Decorativo Redondo", UnitType.Unit, 12.00m),
//       new Product(3, "Plato Base Acrílico Córdoba Filo Dorado", "Plato Base Acrílico Transparente Flor", "Decorativo Redondo", UnitType.Unit, 15.00m),
//       new Product(3, "Plato Base Venecia Filo Dorado", "Plato Base Cristal Filo Dorado", "Decorativo Redondo", UnitType.Unit, 27.00m),
//       new Product(3, "Plato Base Venecia Filo Negro", "Plato Base Cristal Filo Negro", "Decorativo Redondo", UnitType.Unit, 27.00m),
//       new Product(3, "Plato Base Córdoba Blanco", "Plato Base Cristal Filo Blanco", "Decorativo Redondo", UnitType.Unit, 14.00m),
//       new Product(3, "Plato Base Córdoba Negro", "Plato Base Cristal Filo Negro", "Decorativo Redondo", UnitType.Unit, 14.00m),
//       new Product(3, "Plato Base Montecarlo Plata", "Plato Base Montecarlo Plata Redondo", "Decorativo Redondo", UnitType.Unit, 27.00m),
//       new Product(3, "Plato Base Montecarlo Dorado", "Plato Base Montecarlo Dorado Redondo", "Decorativo Redondo", UnitType.Unit, 27.00m),
//
//       // Cubiertos Básicos
//       new Product(4, "Tenedor Básico", "Tenedor Acero Inoxidable", "Cubiertos Básico", UnitType.Unit, 3.00m),
//       new Product(4, "Cuchillo Básico", "Cuchillo Acero Inoxidable", "Cubiertos Básico", UnitType.Unit, 3.00m),
//       new Product(4, "Cuchara Sopera Básica", "Cuchara Sopera Acero Inoxidable", "Cubiertos Básico", UnitType.Unit, 3.00m),
//       new Product(4, "Cuchara Postre/Café Básica", "Cuchara Postre Acero Inoxidable", "Cubiertos Básico", UnitType.Unit, 3.00m),
//
//       // Cubiertos Premium Plata
//       new Product(5, "Tenedor Plata", "Tenedor Acero Inoxidable Plata", "Cubiertos Premium", UnitType.Unit, 6.00m),
//       new Product(5, "Cuchillo Plata", "Cuchillo Acero Inoxidable Plata", "Cubiertos Premium", UnitType.Unit, 6.00m),
//       new Product(5, "Cuchara Sopera Plata", "Cuchara Sopera Acero Inoxidable Plata", "Cubiertos Premium", UnitType.Unit, 6.00m),
//       new Product(5, "Cuchara Postre/Café Plata", "Cuchara Postre Acero Inoxidable Plata", "Cubiertos Premium", UnitType.Unit, 6.00m),
//
//       // Cubiertos Premium Oro
//       new Product(5, "Tenedor Oro", "Tenedor Acero Inoxidable Oro", "Cubiertos Premium", UnitType.Unit, 6.00m),
//       new Product(5, "Cuchillo Oro", "Cuchillo Acero Inoxidable Oro", "Cubiertos Premium", UnitType.Unit, 6.00m),
//       new Product(5, "Cuchara Sopera Oro", "Cuchara Sopera Acero Inoxidable Oro", "Cubiertos Premium", UnitType.Unit, 6.00m),
//       new Product(5, "Cuchara Postre/Café Oro", "Cuchara Postre Acero Inoxidable Oro", "Cubiertos Premium", UnitType.Unit, 6.00m),
//
//       // Additional products to reach 100 entries
//       // I'll create more products by varying the items above and adding new ones
//
//       // Glassware
//       new Product(1, "Copa Vino Tinto", "Copa para vino tinto cristal fino", "Cristalería", UnitType.Unit, 4.00m),
//       new Product(1, "Copa Vino Blanco", "Copa para vino blanco cristal fino", "Cristalería", UnitType.Unit, 4.00m),
//       new Product(1, "Copa Champaña", "Copa flauta para champaña", "Cristalería", UnitType.Unit, 4.50m),
//       new Product(1, "Vaso Highball", "Vaso alto para cocteles", "Cristalería", UnitType.Unit, 3.50m),
//       new Product(1, "Vaso Old Fashioned", "Vaso corto para whisky", "Cristalería", UnitType.Unit, 3.50m),
//       new Product(1, "Copa Martini", "Copa para martini cristal fino", "Cristalería", UnitType.Unit, 5.00m),
//
//       // Linens
//       new Product(2, "Mantel Blanco Rectangular", "Mantel blanco para mesa rectangular", "Mantelería", UnitType.Unit, 15.00m),
//       new Product(2, "Mantel Ivory Redondo", "Mantel color marfil para mesa redonda", "Mantelería", UnitType.Unit, 17.00m),
//       new Product(2, "Servilleta de Tela Blanca", "Servilleta de tela algodón blanca", "Mantelería", UnitType.Unit, 1.50m),
//       new Product(2, "Cubremantel Dorado", "Cubremantel dorado para decoración", "Mantelería", UnitType.Unit, 20.00m),
//       new Product(2, "Camino de Mesa", "Camino de mesa estampado", "Mantelería", UnitType.Unit, 5.00m),
//
//       // Furniture
//       new Product(3, "Mesa Redonda 8 personas", "Mesa redonda para 8 personas", "Mobiliario", UnitType.Unit, 25.00m),
//       new Product(3, "Silla Tiffany Blanca", "Silla Tiffany color blanco", "Mobiliario", UnitType.Unit, 6.00m),
//       new Product(3, "Silla Avant Garde Negra", "Silla Avant Garde color negro", "Mobiliario", UnitType.Unit, 7.00m),
//       new Product(3, "Periquera", "Silla alta para bebés", "Mobiliario", UnitType.Unit, 10.00m),
//       new Product(3, "Sala Lounge", "Sala modular para eventos", "Mobiliario", UnitType.Unit, 100.00m),
//
//       // Iluminacion
//       new Product(4, "Lámpara de Araña", "Lámpara de araña de cristal", "Iluminación", UnitType.Unit, 150.00m),
//       new Product(4, "Reflector LED", "Reflector LED de colores", "Iluminación", UnitType.Unit, 20.00m),
//       new Product(4, "Guirnalda de Luces", "Guirnalda de luces cálidas", "Iluminación", UnitType.Unit, 15.00m),
//       new Product(4, "Candelabro de Mesa", "Candelabro para centro de mesa", "Iluminación", UnitType.Unit, 10.00m),
//       new Product(4, "Antorcha de Jardín", "Antorcha para iluminación exterior", "Iluminación", UnitType.Unit, 12.00m),
//
//       // Equipo de sonido
//       new Product(5, "Bocina Profesional", "Bocina de alta potencia", "Sonido", UnitType.Unit, 50.00m),
//       new Product(5, "Micrófono Inalámbrico", "Micrófono inalámbrico profesional", "Sonido", UnitType.Unit, 25.00m),
//       new Product(5, "Consola de Mezcla", "Consola mezcladora de audio", "Sonido", UnitType.Unit, 100.00m),
//       new Product(5, "Unit DJ", "Equipo completo para DJ", "Sonido", UnitType.Unit, 200.00m),
//       new Product(5, "Karaoke", "Equipo de karaoke con pantalla", "Sonido", UnitType.Unit, 150.00m),
//
//       // Decoracións
//       new Product(1, "Arco de Globos", "Arco decorativo con globos", "Decoración", UnitType.Unit, 80.00m),
//       new Product(1, "Centros de Mesa Florales", "Centro de mesa con flores naturales", "Decoración", UnitType.Unit, 15.00m),
//       new Product(1, "Cortina de Luces LED", "Cortina luminosa para fondo", "Decoración", UnitType.Unit, 25.00m),
//       new Product(1, "Alfombra Roja", "Alfombra roja para eventos", "Decoración", UnitType.Unit, 50.00m),
//       new Product(1, "Photo Booth", "Cabina de fotos instantáneas", "Decoración", UnitType.Unit, 250.00m),
//
//       // Catering Equipment
//       new Product(2, "Chafing Dish", "Recipiente para mantener comida caliente", "Catering", UnitType.Unit, 20.00m),
//       new Product(2, "Fuente de Chocolate", "Fuente para chocolate derretido", "Catering", UnitType.Unit, 60.00m),
//       new Product(2, "Fuente de Bebidas", "Fuente para servir bebidas", "Catering", UnitType.Unit, 50.00m),
//       new Product(2, "Termo para Café", "Dispensador térmico para café", "Catering", UnitType.Unit, 15.00m),
//       new Product(2, "Horno de Convección", "Horno portátil de convección", "Catering", UnitType.Unit, 100.00m),
//
//       // Entretenimiento
//       new Product(2, "Inflable Castillo", "Castillo inflable para niños", "Entretenimiento", UnitType.Unit, 120.00m),
//       new Product(2, "Mesa de Billar", "Mesa de billar profesional", "Entretenimiento", UnitType.Unit, 80.00m),
//       new Product(2, "Pista de Baile LED", "Pista de baile iluminada con LED", "Entretenimiento", UnitType.Unit, 200.00m),
//       new Product(2, "Máquina de Humo", "Máquina para efectos de humo", "Entretenimiento", UnitType.Unit, 30.00m),
//       new Product(2, "Carrito de Palomitas", "Carrito para hacer palomitas", "Entretenimiento", UnitType.Unit, 50.00m),
//
//       // Staffing
//       new Product(3, "Mesero Profesional", "Servicio de mesero por hora", "Personal", UnitType.Hour, 15.00m),
//       new Product(3, "Bartender", "Servicio de bartender por hora", "Personal", UnitType.Hour, 20.00m),
//       new Product(3, "Chef en Sitio", "Servicio de chef en el evento", "Personal", UnitType.Hour, 30.00m),
//       new Product(3, "Animador", "Animador para fiestas infantiles", "Personal", UnitType.Hour, 25.00m),
//       new Product(3, "Seguridad", "Personal de seguridad por hora", "Personal", UnitType.Hour, 18.00m),
//
//       // Additional Furniture
//       new Product(4, "Carpa Blanca 5x5m", "Carpa blanca para eventos al aire libre", "Mobiliario", UnitType.Unit, 150.00m),
//       new Product(4, "Periquera Vintage", "Silla alta vintage para bebés", "Mobiliario", UnitType.Unit, 12.00m),
//       new Product(4, "Mesa Imperial", "Mesa rectangular larga para banquetes", "Mobiliario", UnitType.Unit, 30.00m),
//       new Product(4, "Sillón Chesterfield", "Sofá elegante para lounge", "Mobiliario", UnitType.Unit, 80.00m),
//       new Product(4, "Banco Alto", "Banco alto para barra", "Mobiliario", UnitType.Unit, 8.00m),
//
//       // Additional Decoracións
//       new Product(5, "Letras Gigantes 'LOVE'", "Letras gigantes iluminadas", "Decoración", UnitType.Unit, 100.00m),
//       new Product(5, "Pizarrón Vintage", "Pizarrón para mensajes y menú", "Decoración", UnitType.Unit, 15.00m),
//       new Product(5, "Arreglo Floral Colgante", "Decoración floral suspendida", "Decoración", UnitType.Unit, 50.00m),
//       new Product(5, "Camino de Luz LED", "Iluminación para pasillos", "Decoración", UnitType.Unit, 40.00m),
//       new Product(5, "Telón de Fondo Personalizado", "Telón de fondo con diseño personalizado", "Decoración", UnitType.Unit, 70.00m),
//
//       // Additional Glassware
//       new Product(1, "Decantador de Vino", "Decantador de cristal para vino", "Cristalería", UnitType.Unit, 10.00m),
//       new Product(1, "Jarra de Agua", "Jarra de cristal para agua", "Cristalería", UnitType.Unit, 5.00m),
//       new Product(1, "Copa de Tequila", "Copa caballito para tequila", "Cristalería", UnitType.Unit, 2.00m),
//       new Product(1, "Copa de Cognac", "Copa balón para cognac", "Cristalería", UnitType.Unit, 5.00m),
//       new Product(1, "Copa de Helado", "Copa de cristal para postres", "Cristalería", UnitType.Unit, 3.00m),
//
//       // Additional Catering Equipment
//       new Product(2, "Asador Portátil", "Asador de carbón portátil", "Catering", UnitType.Unit, 80.00m),
//       new Product(2, "Parrilla de Gas", "Parrilla de gas para cocinar", "Catering", UnitType.Unit, 90.00m),
//       new Product(2, "Máquina de Algodón de Azúcar", "Máquina para hacer algodones de azúcar", "Catering", UnitType.Unit, 55.00m),
//       new Product(2, "Refrigerador Portátil", "Refrigerador para mantener bebidas frías", "Catering", UnitType.Unit, 40.00m),
//       new Product(2, "Mesa de Postres", "Mesa decorada para postres", "Catering", UnitType.Unit, 70.00m),
//
//       // Additional Iluminacion
//       new Product(3, "Esfera Disco", "Esfera de espejos para iluminación", "Iluminación", UnitType.Unit, 25.00m),
//       new Product(3, "Luz Estroboscópica", "Luz estroboscópica para efectos", "Iluminación", UnitType.Unit, 18.00m),
//       new Product(3, "Proyector Gobo", "Proyector de imágenes personalizadas", "Iluminación", UnitType.Unit, 35.00m),
//       new Product(3, "Laser Multicolor", "Luz láser para animación", "Iluminación", UnitType.Unit, 40.00m),
//       new Product(3, "Columnas LED", "Columnas luminosas decorativas", "Iluminación", UnitType.Unit, 22.00m),
//
//       // Additional Equipo de sonido
//       new Product(4, "Monitores de Escenario", "Monitores de piso para músicos", "Sonido", UnitType.Unit, 30.00m),
//       new Product(4, "Sistema de Microfonía", "Unit de micrófonos para banda", "Sonido", UnitType.Unit, 120.00m),
//       new Product(4, "Altavoces de Subgraves", "Altavoces para frecuencias bajas", "Sonido", UnitType.Unit, 60.00m),
//       new Product(4, "Auriculares Inalámbricos", "Auriculares para monitoreo", "Sonido", UnitType.Unit, 25.00m),
//       new Product(4, "Soporte de Micrófono", "Base ajustable para micrófono", "Sonido", UnitType.Unit, 5.00m),
//
//       // Additional Linens
//       new Product(5, "Funda para Silla Blanca", "Funda de tela para silla", "Mantelería", UnitType.Unit, 2.00m),
//       new Product(5, "Funda para Silla Negra", "Funda de tela para silla", "Mantelería", UnitType.Unit, 2.00m),
//       new Product(5, "Lazo para Silla Dorado", "Lazo decorativo para silla", "Mantelería", UnitType.Unit, 1.00m),
//       new Product(5, "Lazo para Silla Plateado", "Lazo decorativo para silla", "Mantelería", UnitType.Unit, 1.00m),
//       new Product(5, "Mantel Satinado Rojo", "Mantel satinado color rojo", "Mantelería", UnitType.Unit, 18.00m),
//   };
//     
//     // Add a mapping of provider names to their state and service tags
//   public static Dictionary<string, List<string>> ProviderTagMappings = new()
//   {
//     {
//       "La Casa del Banquete", new List<string>
//       {
//         "State|CDMX", // State tag
//         "Provider|Banquete|CateringPorTiempos", // Catering
//         "Provider|Decoracion|DecoracionFloral", // Decoración
//         "Provider|Meseros|Servicio", // Staffing
//         "Provider|Lugares|Hoteles", // Lugares
//         "Provider|Coordinacion|PlaneadorDeEvento" // Coordinación
//       }
//     },
//     {
//       "Eventos y Más", new List<string>
//       {
//         "State|Jalisco", // State tag
//         "Provider|Audiovisual|EquiposDeSonido", // Equipo de sonido
//         "Provider|Iluminacion|Proyectores", // Iluminacion
//         "Provider|Entretenimiento|Shows", // Entretenimiento
//         "Provider|Ranking|Top", // Proveedor top (added for reference)
//         "Provider|Coordinacion|Asesoria", // Coordinación
//         "Provider|Lugares|Jardines" // Lugares
//       }
//     },
//     {
//       "Banquetes Gourmet", new List<string>
//       {
//         "State|NuevoLeon", // State tag
//         "Provider|Banquete|CateringPorTiempos", // Catering
//         "Provider|Lugares|Haciendas", // Lugares
//         "Provider|Coordinacion|Cronograma", // Coordinación
//         "Provider|Entretenimiento|Actividades" // Entretenimiento
//       }
//     },
//     {
//       "Fiestas y Diversión", new List<string>
//       {
//         "State|Puebla", // State tag
//         "Provider|Entretenimiento|Animadores", // Entretenimiento
//         "Provider|Decoracion|AccesoriosDecorativos", // Decoración
//         "Provider|Meseros|Servicio", // Staffing
//         "Provider|Ranking|Top", // Proveedor top (added for reference)
//         "Provider|Lugares|Terrazas", // Lugares
//         "Provider|Coordinacion|Supervision" // Coordinación
//       }
//     },
//     {
//       "Eventos Especiales", new List<string>
//       {
//         "State|Guanajuato", // State tag
//         "Provider|Lugares|CentrosDeConvenciones", // Lugares
//         "Provider|Iluminacion|LucesLed", // Iluminacion
//         "Provider|Audiovisual|EquiposDeSonido", // Equipo de sonido
//         "Provider|Ranking|Top", // Proveedor top (added for reference)
//         "Provider|Coordinacion|AsesorProtocolo", // Coordinación
//         "Provider|Banquete|Buffet" // Banquete
//       }
//     },
//     // Add mappings for other providers as needed
//   };
// }
