using System.Security.Claims;
using FastEndpoints;
using Fiestr.Core;
using Fiestr.Infrastructure.Data.Queries;
using Microsoft.AspNetCore.Authorization;
namespace Fiestr.Web.Middleware;

public class AddCustomClaimsMiddleware(ICustomClaimsQueryService query) : IMiddleware
{
  public async Task InvokeAsync(HttpContext context, RequestDelegate next)
  {
    var endpoint = context.GetEndpoint();
    if (endpoint == null || endpoint.Metadata.GetMetadata<AllowAnonymousAttribute>() != null)
    {
      await next(context);
      return;
    }

    var userId = context.User.Claims.FirstOrDefault(e => e.Type == Consts.JwtKeys.Sub)?.Value;
    var ct = context.RequestAborted;
    if (userId == null || !Guid.TryParse(userId, out var userGuid))
    {
      await context.Response.SendAsync("Sub from token not found or is not a GUID , try logging again.", 401, cancellation: ct);
      return;
    }

    var qResult = await query.ListAsync(userGuid, ct);
    var claims = new[]
    {
      new Claim(Consts.CustomClaimsKeys.LinkedProviderIds, string.Join(", ", qResult.LinkedProviderIds))
    };
    var customClaimsIdentity = new ClaimsIdentity(claims);
    context.User.AddIdentity(customClaimsIdentity);
    await next(context);
  }
}
