using Ardalis.Result;
using FastEndpoints;
namespace Fiestr.Web.Extensions;

static class ArdalisResultsExtensions
{
  public static async Task SendResultResponse<TResult, TResponse>(
    this IEndpoint ep,
    TResult result,
    Func<TResult, TResponse> mapper,
    CancellationToken ct = new())
    where TResult : Ardalis.Result.IResult
  {
    switch (result.Status)
    {
      case ResultStatus.Ok:
        var resultType = result.GetType();
        var isPagedResult = resultType.IsGenericType &&
          resultType.GetGenericTypeDefinition() == typeof(PagedResult<>);
        if (isPagedResult)
        {
          var pagedInfoProperty = resultType.GetProperty("PagedInfo");
          var pagedInfo = pagedInfoProperty?.GetValue(result);

          var response = new PagedResponse<TResponse>(mapper(result), (PagedInfo)pagedInfo!);

          await ep.HttpContext.Response.SendAsync(response, cancellation: ct);
          return;
        }

        // For non-paged results, just map and send the response
        await ep.HttpContext.Response.SendAsync(mapper(result), cancellation: ct);
        break;

      case ResultStatus.Invalid:
        foreach (var error in result.ValidationErrors)
        {
          ep.ValidationFailures.Add(new(error.Identifier ?? string.Empty, error.ErrorMessage));
        }

        await ep.HttpContext.Response.SendErrorsAsync(ep.ValidationFailures, cancellation: ct);
        break;
      case ResultStatus.Error:

        foreach (var error in result.Errors)
        {
          ep.ValidationFailures.Add(new(string.Empty, error));
        }

        await ep.HttpContext.Response.SendErrorsAsync(ep.ValidationFailures, cancellation: ct);
        break;
      case ResultStatus.Forbidden:
        break;
      case ResultStatus.Unauthorized:
        await ep.HttpContext.Response.SendUnauthorizedAsync(cancellation: ct);
        break;
      case ResultStatus.NotFound:
        await ep.HttpContext.Response.SendNotFoundAsync(ct);
        break;
      case ResultStatus.Conflict:
        break;
      case ResultStatus.CriticalError:
        break;
      case ResultStatus.Unavailable:
        break;
      default:
        throw new ArgumentOutOfRangeException();
    }
  }
}

public class PagedResponse<TResponse>
{
  public TResponse Data { get; set; }
  public PagedInfo PaginationInfo { get; set; }

  public PagedResponse(TResponse data, PagedInfo paginationInfo)
  {
    Data = data;
    PaginationInfo = paginationInfo;
  }
}
