<Project Sdk="Microsoft.NET.Sdk.Web">
  <Sdk Name="Microsoft.Build.CentralPackageVersions" Version="2.1.3" />

  <PropertyGroup>
    <PreserveCompilationContext>true</PreserveCompilationContext>
    <OutputType>Exe</OutputType>
    <WebProjectMode>true</WebProjectMode>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Ardalis.ListStartupServices" />
    <PackageReference Include="Ardalis.Result" />
    <PackageReference Include="Ardalis.Result.AspNetCore" />
    <PackageReference Include="FastEndpoints" />
    <PackageReference Include="FastEndpoints.Swagger" />
    <PackageReference Include="FluentValidation.DependencyInjectionExtensions" />
    <PackageReference Include="MediatR" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" />
    <PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" PrivateAssets="All" />
    <PackageReference Include="Serilog.AspNetCore" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Fiestr.Infrastructure\Fiestr.Infrastructure.csproj" />
    <ProjectReference Include="..\Fiestr.UseCases\Fiestr.UseCases.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Logs\" />
  </ItemGroup>
</Project>
