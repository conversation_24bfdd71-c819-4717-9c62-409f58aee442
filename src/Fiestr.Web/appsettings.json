{
  "Auth": {
    "Fiestr": {
      "Key": "TJMtBbQCFjVY9sFxThz2pZ9KLrcLaazErueBk2KLVdze753pFvmFxwPw9FFSuYZSWkpeoQ4u7buUzvam",
      "Issuer":"https://fiestr.com/",
      "Audience": "https://fiestr.com/",
      "Duration": "1.00:00:00"
    },
    "Okta": {
      "ClientId": "YhvOf2QekVYfJqfLsV9vKLuqR3SSWTMh",
      "ClientSecret": "MiBIyDcfOoWTsG3wKytHO6m62Dgpnw_1YaILgWDlaIhbYRXBfLGCb3hvpBvmJD4o"
    }
  },
  "ConnectionStrings": {
    "SqliteConnection": "Data Source=./fiestr.sqlite"
  },
  "Serilog": {
    "MinimumLevel": {
      "Default": "Information"
    },
    "WriteTo": [
      {
        "Name": "Console",
        "Args": {
          "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff} [{Level:u3}] {Message:lj}{NewLine}{Exception}"
        }
      },
      {
        "Name": "File",
        "Args": {
          "path": "./Logs/log.txt",
          "rollingInterval": "Day"
        }
      }
      //Uncomment this section if you'd like to push your logs to Azure Application Insights
      //Full list of Serilog Sinks can be found here: https://github.com/serilog/serilog/wiki/Provided-Sinks
      //{
      //  "Name": "ApplicationInsights",
      //  "Args": {
      //    "instrumentationKey": "", //Fill in with your ApplicationInsights InstrumentationKey
      //    "telemetryConverter": "Serilog.Sinks.ApplicationInsights.Sinks.ApplicationInsights.TelemetryConverters.TraceTelemetryConverter, Serilog.Sinks.ApplicationInsights"
      //  }
      //}
    ]
  },
  "Mailserver": {
    "Server": "localhost",
    "Port": 25
  },
  "ImageConfiguration": {
    "ThumbnailMaxDimensions": 320,
    "ImageMaxMaxDimensions": 1200,
    "MinBytes": 100,
    "MaxBytes": 10485760,
    "SupportedFormats": [
      "image/jpeg", 
      "image/png"
    ],
    "LossyCompression": false
  }
}
