using Ardalis.Result;
using Ardalis.SharedKernel;
using Fiestr.Core.Aggregates.UserAggregate;
using Fiestr.Core.Aggregates.UserAggregate.Specifications;
using Fiestr.UseCases.Auth;

namespace Fiestr.UseCases.Users.GetById;

public record UserByIdQuery(Guid Id): SharedKernel.IQuery<Result<UserDto>>;

public class UserByIdHandler(IReadRepository<User> userRepo) : SharedKernel.IQueryHandler<UserByIdQuery, Result<UserDto>>
{
  public async Task<Result<UserDto>> Handle(UserByIdQuery query, CancellationToken ct)
  {
    var spec = new UserByIdSpecification(query.Id);
    var entity = await userRepo.SingleOrDefaultAsync(spec, ct);
    if (entity is null) return Result.NotFound();
    var dto = UserDto.MapFrom(entity);
    return Result.Success(dto);
  }
}
