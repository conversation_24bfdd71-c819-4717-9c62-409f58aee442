using Fiestr.Core.Aggregates.UserAggregate;
namespace Fiestr.UseCases.Auth;

public class UserDto
{
  public required string Auth0UserId { get; set; }
  public required string Name { get; set; }
  public required string Nickname { get; set; }
  public required string Email { get; set; }
  
  public static UserDto MapFrom(User user) =>
    new UserDto
    {
      Auth0UserId = user.OktaUserId,
      Name = user.Name, 
      Nickname = user.Nickname, 
      Email = user.Email
    };
}
