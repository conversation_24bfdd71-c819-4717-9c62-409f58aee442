using Ardalis.Result;
using Ardalis.SharedKernel;
using Fiestr.Core.Aggregates.UserAggregate;
using Fiestr.Core.Aggregates.UserAggregate.Specifications;
using Fiestr.Core.Interfaces;
using Fiestr.Core.Models.Auth;
using Fiestr.SharedKernel.Extensions;

namespace Fiestr.UseCases.Auth.Login;

public record LoginCommand(string Code, string RedirectUri) : ICommand<Result<OAuthResponse>>;

public class LoginHandler(IAuthService authService, IRepository<User> userRepo): ICommandHandler<LoginCommand, Result<OAuthResponse>>
{
  public async Task<Result<OAuthResponse>> Handle(LoginCommand cmd, CancellationToken ct)
  {
    var challengeResult = await authService.AuthorizeWithCode(cmd.Code, cmd.RedirectUri, ct);
    if (!challengeResult.IsSuccess)
      return challengeResult.MapToEmptyResult();

    var user = await UpsertUserFromChallenge(challengeResult, ct);
    
    var result = authService.OIdTokensForUser(user);

    return result;
  }
  
  private async Task<User> UpsertUserFromChallenge(ExternalFederationAuthResult authResult, CancellationToken ct)
  {
    var spec = new UserByOktaIdSpecification(authResult.UserId);
    var user = await userRepo.FirstOrDefaultAsync(spec, ct);
    if (user is null)
    {
      user = new User(
        authResult.UserId,
        authResult.Name,
        authResult.Email,
        authResult.Nickname);
      await userRepo.AddAsync(user, ct);
    }
    else
    {
      user.Update(
        authResult.UserId,
        authResult.Name,
        authResult.Email,
        authResult.Nickname);
    }
    return user;
  }

}
