using Ardalis.Result;
using Ardalis.SharedKernel;
using Fiestr.Core.Aggregates.ProviderAggregate;
using Fiestr.Core.Aggregates.ProviderAggregate.Specs;
namespace Fiestr.UseCases.Providers.Update;

public record ProviderUpdateCommand(int Id, string Name, string PhoneNumber, string Address1, List<int> TagIds) : ICommand<Result>;

public class ProviderUpdateHandler(IRepository<Provider> repo): ICommandHandler<ProviderUpdateCommand, Result>
{
  public async Task<Result> Handle(ProviderUpdateCommand command, CancellationToken ct)
  {
    var spec = new ProviderByIdSpec(command.Id);
    var entity = await repo.FirstOrDefaultAsync(spec, ct);
    if (entity == null)
    {
      return Result.NotFound();
    }
    entity.Update(command.Name, command.PhoneNumber, command.Address1);
    entity.SetTags(command.TagIds);
    await repo.UpdateAsync(entity, ct);
    return Result.Success();
  }
}
