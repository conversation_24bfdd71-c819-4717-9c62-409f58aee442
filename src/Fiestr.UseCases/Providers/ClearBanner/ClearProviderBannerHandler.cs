using Ardalis.Result;
using Ardalis.SharedKernel;
using Fiestr.Core.Aggregates.ProviderAggregate;
using Fiestr.Core.Aggregates.ProviderAggregate.Specs;

namespace Fiestr.UseCases.Providers.ClearBanner;

public class ClearProviderBannerHandler(IRepository<Provider> repo) : ICommandHandler<ClearProviderBannerCommand, Result>
{
  public async Task<Result> Handle(ClearProviderBannerCommand command, CancellationToken ct)
  {
    var spec = new ProviderByIdSpec(command.ProviderId);
    var provider = await repo.FirstOrDefaultAsync(spec, ct);
    if (provider == null)
      return Result.NotFound();
    
    provider.ClearBanner();
    await repo.UpdateAsync(provider, ct);
    return Result.Success();
  }
}
