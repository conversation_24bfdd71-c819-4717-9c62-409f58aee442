using Ardalis.Result;
using Ardalis.SharedKernel;
using Fiestr.Core.Aggregates.ProviderAggregate.Specs;
using Fiestr.UseCases.Providers.Search;

namespace Fiestr.UseCases.Providers.GetById;

public class ProviderByIdQueryHandler(IProviderSearchQueryService service) : IQueryHandler<ProviderByIdQuery, Result<ProviderSearchDto>>
{
  public Task<Result<ProviderSearchDto>> Handle(ProviderByIdQuery q, CancellationToken ct)
  {
    var qResult = service.List(new ProviderSearchQuery { Id = q.Id, PageNumber = 1, PageSize = 1});
    var result = qResult.Value.FirstOrDefault();
    return result is null 
      ? Task.FromResult<Result<ProviderSearchDto>>(Result.NotFound()) 
      : Task.FromResult(Result.Success(result));
  }
}
