using Ardalis.Result;
using Ardalis.SharedKernel;
using Fiestr.Core.Aggregates.ProviderAggregate;
using Fiestr.Core.Interfaces;
namespace Fiestr.UseCases.Providers.Create;

public class CreateProviderHandler(IRepository<Provider> repository, IIdentityUser user) 
  : ICommandHandler<CreateProviderCommand, Result<int>>
{
  public async Task<Result<int>> Handle(CreateProviderCommand command, CancellationToken ct)
  {
    // if (user.LinkedProviderIds.Length > 0)
    // {
    //   return Result.Invalid(new ValidationError("Users can only have one provider linked at a time."));
    // }
    
    var entity = new Provider(command.Name, command.PhoneNumber, command.Address1);
    entity.LinkUser(user.Id);
    entity.SetTags(command.TagIds);
    var createdItem = await repository.AddAsync(entity, ct);
    return createdItem.Id;
  }
}

