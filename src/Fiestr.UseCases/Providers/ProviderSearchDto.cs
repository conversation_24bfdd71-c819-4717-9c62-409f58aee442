using Fiestr.Core.Aggregates.ProviderAggregate;
using Fiestr.UseCases.GeneralModels;

namespace Fiestr.UseCases.Providers;

public record ProviderSearchDto
{
  public List<int> TagIds { get; set; } = [];
  public List<ImageReferenceDto> Images { get; set; }= [];
  public ImageReferenceDto? Logo { get; set; }
  public ImageReferenceDto? Banner { get; set; }
  public required int Id { get; set;}
  public required string Name { get; set;}
  public required string PhoneNumber { get; set;}
  public required string Address1 { get; set;}
  public required int? Rating { get; set;}
  public int RatingCount { get; set; }
  public DateTime CreatedAt { get; set; }
}
