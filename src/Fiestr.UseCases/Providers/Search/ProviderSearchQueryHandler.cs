using Ardalis.Result;
using Fiestr.SharedKernel;
namespace Fiestr.UseCases.Providers.Search;

public class ProviderSearchQueryHandler(IProviderSearchQueryService qService) : IQueryHandler<ProviderSearchQuery, PagedResult<List<ProviderSearchDto>>>
{
  public Task<PagedResult<List<ProviderSearchDto>>> Handle(ProviderSearchQuery request, CancellationToken ct)
  {
    var result = qService.List(request);
    return Task.FromResult(result);
  }
}
