using Ardalis.Result;
using Fiestr.SharedKernel;
using FluentValidation;

namespace Fiestr.UseCases.Providers.Search;

public record ProviderSearchQuery : IPaginatedQuery<PagedResult<List<ProviderSearchDto>>>
{
  public int? Id { get; set; }
  public string? SearchTerm { get; set; }
  public int PageNumber { get; set; }
  public int PageSize { get; set; }
  public string? OrderBy { get; set; }
  public string? OrderType { get; set; }
  public int? MinRating { get; set; }
  public int? MaxRating { get; set; }
  public List<int> TagIds { get; set; } = [];
}

public class Validator : AbstractValidator<ProviderSearchQuery>
{
  public Validator()
  {
    this.When(e => e.MinRating is not null, () =>
    {
      this.RuleFor(e => e.MinRating)
        .GreaterThanOrEqualTo(0)
        .LessThanOrEqualTo(100);
    });
    
    this.When(e => e.MinRating is not null && e.MaxRating is not null, () =>
    {
      this.RuleFor(e => e.MinRating)
        .LessThanOrEqualTo(e => e.MaxRating);
    });
    
    this.When(e => e.MaxRating is not null, () =>
    {
      this.RuleFor(e => e.MaxRating)
        .GreaterThanOrEqualTo(0)
        .LessThanOrEqualTo(100);
    });
  }
}
