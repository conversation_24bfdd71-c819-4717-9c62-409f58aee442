using Ardalis.Result;
using Ardalis.SharedKernel;
using Fiestr.Core.Aggregates.ProviderAggregate;
using Fiestr.Core.Aggregates.ProviderAggregate.Specs;
using Fiestr.Core.Interfaces;

namespace Fiestr.UseCases.Providers.SetBanner;

public class SetProviderBannerHandler(IRepository<Provider> repo, IImageService imgService) : ICommandHandler<SetProviderBannerCommand, Result>
{
  public async Task<Result> Handle(SetProviderBannerCommand command, CancellationToken ct)
  {
    var spec = new ProviderByIdSpec(command.ProviderId);
    var provider = await repo.FirstOrDefaultAsync(spec, ct);
    if (provider == null)
      return Result.NotFound();
    
    var imgId = await imgService.ProcessMainImageAsync(command.Img, ct);
    var thumbNailId = await imgService.ProcessThumbnailAsync(command.Img, ct);
    provider.SetBanner(thumbNailId, imgId);
    await repo.UpdateAsync(provider, ct);
    return Result.Success();
  }
}
