using Ardalis.Result;
using Ardalis.SharedKernel;
using Fiestr.Core.Aggregates.ProviderAggregate;
using Fiestr.Core.Aggregates.ProviderAggregate.Specs;
using Fiestr.Core.Interfaces;
namespace Fiestr.UseCases.Providers.SetImage;

public class SetProviderImageHandler(IRepository<Provider> repo, IImageService imgService) : ICommandHandler<SetProviderImageCommand, Result>
{
  public async Task<Result> Handle(SetProviderImageCommand command, CancellationToken ct)
  {
    var spec = new ProviderByIdSpec(command.ProviderId);
    var provider = await repo.FirstOrDefaultAsync(spec, ct);
    if (provider == null)
      return Result.NotFound();
    
    // Only allowing one image to be set for now
    provider.ClearImages();
    var imgId = await imgService.ProcessMainImageAsync(command.Img, ct);
    var thumbNailId = await imgService.ProcessThumbnailAsync(command.Img, ct);
    provider.AddImage(thumbNailId, imgId);
    await repo.UpdateAsync(provider, ct);
    return Result.Success();
  }
}
