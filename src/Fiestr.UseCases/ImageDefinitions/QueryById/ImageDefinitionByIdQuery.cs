using Ardalis.Result;
using Ardalis.SharedKernel;
using Fiestr.Core.Aggregates.ImageDetailAggregate;
namespace Fiestr.UseCases.ImageDefinitions.QueryById;

public record ImageDefinitionByIdQuery(string Id) : SharedKernel.IQuery<Result<ImageDefinitionDto>>;

public class ImageDefinitionByIdHandler(IImageDefinitionByIdQueryService service) : SharedKernel.IQueryHandler<ImageDefinitionByIdQuery, Result<ImageDefinitionDto>>
{
  public async Task<Result<ImageDefinitionDto>> Handle(ImageDefinitionByIdQuery query, CancellationToken ct)
  {
    var result = await service.GetAsync(query.Id, ct);
    return result;
  }
}

public interface IImageDefinitionByIdQueryService
{
  Task<Result<ImageDefinitionDto>> GetAsync(string id, CancellationToken cancellationToken = default);
}

