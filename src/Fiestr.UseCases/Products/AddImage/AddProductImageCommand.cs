using Ardalis.Result;
using Ardalis.SharedKernel;
using Fiestr.Core.Aggregates.ProductAggregate;
using Fiestr.Core.Interfaces;
using Fiestr.SharedKernel.Extensions;
using FluentValidation;
namespace Fiestr.UseCases.Products.AddImage;

public record AddProductImageCommand(int ProductId, Stream ImgStream): SharedKernel.ICommand<Result>;

public class AddProductImageHandler(IRepository<Product> repo, IImageService imgService): SharedKernel.ICommandHandler<AddProductImageCommand, Result>
{
  public async Task<Result> Handle(AddProductImageCommand command, CancellationToken ct)
  {
    var spec = new ProductByIdSpec(command.ProductId);
    var entity = await repo.FirstOrDefaultAsync(spec, ct);
    if (entity is null)
      return Result.NotFound();

    var imgId = await imgService.ProcessMainImageAsync(command.ImgStream, ct);
    var thumbnailId = await imgService.ProcessThumbnailAsync(command.ImgStream, ct);
    var addImgResult = entity.AddImage(thumbnailId, imgId);
    if (!addImgResult.IsSuccess)
      return addImgResult.MapToEmptyResult();
    await repo.UpdateAsync(entity, ct);
    return Result.Success();
  }
}

public class AddProductImageValidator : AbstractValidator<AddProductImageCommand>
{
  public AddProductImageValidator(IIdentityUser user, IUserCanEditProductQueryService userEditQuery)
  {

    this
      .RuleFor(e => e.ProductId)
      .MustAsync(async (e, ct) => await userEditQuery.UserCanEditProduct(user.Id, e, ct))
      .WithMessage("User does not have access to this product");
  }
}
