using Ardalis.Result;
using Ardalis.SharedKernel;
using Fiestr.Core.Aggregates.ProductAggregate;

namespace Fiestr.UseCases.Products.RemoveImage;

public record RemoveProductImageCommand(int ProductId, string ImageId): SharedKernel.ICommand<Result>;

public class RemoveProductImageHandler(IRepository<Product> repo) : SharedKernel.ICommandHandler<RemoveProductImageCommand, Result>
{
  public async Task<Result> Handle(RemoveProductImageCommand command, CancellationToken ct)
  {
    var entity = await repo.SingleOrDefaultAsync(new ProductByIdSpec(command.ProductId), ct);
    if (entity is null || !entity.Images.Any(e => e.ImageId == command.ImageId))
    {
      return Result.NotFound();
    }

    var img = entity.Images.Single(e => e.ImageId == command.ImageId);
    entity.RemoveImage(img);
    await repo.SaveChangesAsync(ct);
    return Result.Success();
  }
}
