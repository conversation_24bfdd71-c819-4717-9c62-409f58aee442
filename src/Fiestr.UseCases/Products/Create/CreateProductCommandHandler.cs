using Ardalis.Result;
using Ardalis.SharedKernel;
using Fiestr.Core.Aggregates.ProductAggregate;
namespace Fiestr.UseCases.Products.Create;

public class CreateProductCommandHandler(IRepository<Product> repo): ICommandHandler<CreateProductCommand, Result<int>>
{
  public async Task<Result<int>> Handle(CreateProductCommand r, CancellationToken cancellationToken)
  {
    var product = new Product(r.ProviderId, r.Name, r.Description, r.LongDescription, r.Sku, r.UnitType, r.UnitPrice);
    await repo.AddAsync(product, cancellationToken);
    return product.Id;
  }
}
