using Ardalis.SharedKernel;
using Fiestr.Core.Aggregates.ProviderAggregate;
using Fiestr.Core.Aggregates.ProviderAggregate.Specs;
using Fiestr.Core.Interfaces;
using FluentValidation;
namespace Fiestr.UseCases.Products.Create;

public class CreateProductValidator : AbstractValidator<CreateProductCommand>
{
  public CreateProductValidator(IReadRepository<Provider> providerRepo, IIdentityUser user)
  {
    this.RuleFor(e => e.ProviderId)
      .Cascade(CascadeMode.Stop)
      .MustAsync(async (providerId, ct) =>
      {
        var spec = new ProviderByIdSpec(providerId);
        return await providerRepo.AnyAsync(spec, ct);
      })
        .WithMessage("The provider doesn't exist")
      .Must(providerId => user.LinkedProviderIds.Contains(providerId))
        .WithMessage("The user doesn't have access to this provider");
  }
}
