using Ardalis.Result;
using Ardalis.SharedKernel;
using Fiestr.Core.Enums;

namespace Fiestr.UseCases.Products.Create;

public record CreateProductCommand: ICommand<Result<int>>
{
  public required string Name { get; set;}
  public required int ProviderId { get; set;}
  public required string Description { get; set;}
  public string? LongDescription { get; set;}
  public string? Sku { get; set;}
  public required UnitType UnitType { get; set;}
  public required decimal UnitPrice { get; set;}
}
