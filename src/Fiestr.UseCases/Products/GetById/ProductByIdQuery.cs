using Ardalis.Result;
using Ardalis.SharedKernel;
using Fiestr.Core.Aggregates.ProductAggregate;
namespace Fiestr.UseCases.Products.GetById;

public record ProductByIdQuery(int ProductId): IQuery<Result<ProductDto>>;

public class ProductByIdHandler(IGetProductByIdQueryService service) : IQueryHandler<ProductByIdQuery, Result<ProductDto>>
{
  public Task<Result<ProductDto>> Handle(ProductByIdQuery request, CancellationToken ct)
  {
    var result = service.Get(request.ProductId);
    return Task.FromResult(result);
  }
}
