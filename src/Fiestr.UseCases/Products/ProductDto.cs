using Fiestr.Core.Enums;
using Fiestr.UseCases.GeneralModels;

namespace Fiestr.UseCases.Products;

public record ProductDto
{
  public required int Id { get; init; }
  public required int ProviderId { get; init; }
  public required string Name { get; init; }
  public required string Description { get; init; }
  public string? LongDescription { get; init; }
  public required UnitType UnitType { get; init; } 
  public required decimal UnitPrice { get; init; }
  public List<ImageReferenceDto> Images { get; set; } = [];
  public List<int> TagIds { get; set; } = [];
  public DateTime CreatedAt { get; set; }
}
