namespace Fiestr.Core.Enums;

public enum UnitType
{
  <PERSON>za = 1,
  <PERSON><PERSON> = 2,
  <PERSON><PERSON><PERSON> = 3,
  <PERSON>a = 4,
  Set = 5
}

public static class UnitTypeExtensions
{
  public static UnitType ToUnitType(this string val)
  {
    return val.ToLowerInvariant() switch
    {
      "pieza" => UnitType.Pieza,
      "hora" => UnitType.Hora,
      "servicio" => UnitType.Servicio,
      "persona" => UnitType.Persona,
      "set" => UnitType.Set,
      _ => throw new ArgumentException($"Invalid unit type {val}", nameof(val))
    };
  }
}
