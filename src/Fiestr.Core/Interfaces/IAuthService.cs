using Ardalis.Result;
using Fiestr.Core.Aggregates.UserAggregate;
using Fiestr.Core.Models.Auth;
namespace Fiestr.Core.Interfaces;

public interface IAuthService
{
  Task<Result<ExternalFederationAuthResult>> GetUserInfo(string accessToken, CancellationToken ct);
  OAuthResponse OIdTokensForUser(User user);
  Task<Result<ExternalFederationAuthResult>> AuthorizeWithCode(string code, string redirectUri, CancellationToken ct);
}
