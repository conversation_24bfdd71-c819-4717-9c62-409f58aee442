using Fiestr.Core.Models;
namespace Fiestr.Core.Interfaces;

public interface IImageService
{
  Task DeleteAsync(string imgId, CancellationToken ct = default);
  Task<string> ProcessMainImageAsync(Stream imgStream, CancellationToken ct = default);
  Task<string> ProcessThumbnailAsync(Stream imgStream, CancellationToken ct = default);
  Task<Stream> Resize(Stream imgStream, uint dimensionLimit, CancellationToken ct = default);
  Stream Compress(Stream imgStream);
}
