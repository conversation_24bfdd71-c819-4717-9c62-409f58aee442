using System.Security.Claims;
namespace Fiestr.Core;

public static class Consts
{
  public static class CustomClaimsKeys
  {
    public const string LinkedProviderIds = "LinkedProviderIds";
  }
  public static class JwtKeys
  {
    public const string Sub = ClaimTypes.NameIdentifier;
    public const string OktaUserId = "auth0UserId";
    public const string Name = "name";
    public const string Nickname = "nickname";
    public const string Email = "email";
    public const string Scope = "scope";
  }

  public static class JwtScopes
  {
    public const string MyAccountManage = "fiestr.myAccount.manage";
  }

  public static class OktaScopes
  {
    public const string OpenId = "openid";
    public const string Profile = "profile";
    public const string Email = "email";
  }

  public static class ThumbnailSize
  {
    public static class Provider
    {
      public const int H = 300;
      public const int W = 300;
    }
  }
}
