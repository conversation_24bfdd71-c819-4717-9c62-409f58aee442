using Ardalis.SharedKernel;
namespace Fiestr.Core.Aggregates.UserAggregate;

public class User : EntityBase<Guid>, IAggregateRoot
{
  public User(string oktaUserId, string name, string email, string nickname)
  {
    OktaUserId = oktaUserId;
    Name = name;
    Email = email;
    Nickname = nickname;
  }
  public void Update(string auth0UserId, string name, string email, string nickname)
  {
    OktaUserId = auth0UserId;
    Name = name;
    Email = email;
    Nickname = nickname;
  }
  public string OktaUserId { get; private set; }
  public string Name { get; private set; }
  public string Nickname { get; private set; }
  public string Email { get; private set; }
}
