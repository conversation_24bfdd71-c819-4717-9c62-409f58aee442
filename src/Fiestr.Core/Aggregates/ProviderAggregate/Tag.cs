using Ardalis.SharedKernel;
using Fiestr.SharedKernel.Extensions;
namespace Fiestr.Core.Aggregates.ProviderAggregate;

public class Tag : EntityBase
{
  public Tag(TagEntityType entityType, string category, string code, string name)
  {
    Name = name;
    Category = category;
    Code = code;
    EntityType = entityType;
  }
  public string Name { get; private set; }
  public string Category { get; private set; }
  public string Code { get; private set; }
  public TagEntityType EntityType { get; private set; }
}

public enum TagEntityType : long
{
  None = 0,
  Provider = 1,
  Product = 2,
}
