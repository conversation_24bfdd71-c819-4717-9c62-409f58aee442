using Ardalis.GuardClauses;
using Ardalis.SharedKernel;
using Fiestr.Core.Aggregates.ProviderAggregate.Events;
namespace Fiestr.Core.Aggregates.ProviderAggregate;

public class Provider : EntityBase, IAggregateRoot
{
  private Provider()
  {
  }
  
  public Provider(string name, string phoneNumber, string address1)
  {
    Name = name;
    PhoneNumber = phoneNumber;
    Address1 = address1;
    CreatedAt = DateTime.UtcNow;
  }
  
  public void Update(string name, string phoneNumber, string address1)
  {
    Name = name;
    PhoneNumber = phoneNumber;
    Address1 = address1;
  }
  
  public string Name { get; private set; } = null!;
  public string PhoneNumber { get; private set; } = null!;
  public string Address1 { get; private set; } = null!;
  public DateTime CreatedAt { get; private set; }

  private readonly List<ProviderUsers> _providerUsers = [];
  private readonly List<ProviderTags> _providerTags = [];
  
  // Provider users
  public IReadOnlyCollection<Guid> ProviderUsers => _providerUsers.Select(e => e.UserId).ToList().AsReadOnly();

  public void LinkUser(Guid userId)
  {
    this._providerUsers.Add(new ProviderUsers{ProviderId = this.Id, UserId = userId});
  }

  public void RemoveUser(Guid userId)
  {
    var links = _providerUsers.Where(e => e.UserId == userId);
    foreach (var link in links)
    {
      this._providerUsers.Remove(link);
    }
  }
  
  // TAGS
  public IReadOnlyCollection<int> TagIds => _providerTags.Select(e => e.TagId).ToList().AsReadOnly();
  public void SetTags(List<int> tagIds)
  {
    _providerTags.Clear();
    var tags = tagIds.Select(tagId => new ProviderTags
    {
      TagId = tagId
    });
    _providerTags.AddRange(tags);
  }
  
  public void RemoveTags(List<int> tagIds)
  {
    _providerTags.RemoveAll(pt => tagIds.Contains(pt.TagId));
  }
  
  // LOGO
  public ProviderImage? Logo { get; private set; }

  public void SetLogo(string thumbnailId, string imageId)
  {
    Logo = new ProviderImage(thumbnailId, imageId);
  }

  // BANNER
  public ProviderImage? Banner { get; private set; }

  public void SetBanner(string thumbnailId, string imageId)
  {
    Banner = new ProviderImage(thumbnailId, imageId);
  }

  public void ClearBanner()
  {
    Banner = null;
  }
  
  // IMAGES
  private readonly List<ProviderImage> _images = [];
  public IReadOnlyCollection<ProviderImage> Images => _images.AsReadOnly();
  public void AddImage(string thumbnailId, string imageId)
  {
    var image = new ProviderImage(thumbnailId, imageId);
    _images.Add(image);
  }
  
  public void RemoveImage(ProviderImage image)
  {
    this.RegisterDomainEvent(new ProviderImageDeletedEvent(image));
    _images.Remove(image);
  }
  public void ClearImages()
  {
    foreach (var img in Images)
    {
      this.RegisterDomainEvent(new ProviderImageDeletedEvent(img));
    }
    _images.Clear();
  }
}

public class ProviderUsers
{
  public Guid UserId { get; set; }
  public int ProviderId { get; set; }
}

public class ProviderTags
{
  public int ProviderId { get; set; }
  public int TagId { get; set; }
}

public class ProviderImage(string thumbnailId, string imageId)
{
  public string ThumbnailId { get; set; } = thumbnailId;
  public string ImageId { get; set; } = imageId;
}
