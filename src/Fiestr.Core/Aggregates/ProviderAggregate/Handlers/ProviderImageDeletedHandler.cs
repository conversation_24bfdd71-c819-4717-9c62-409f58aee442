using Fiestr.Core.Aggregates.ProviderAggregate.Events;
using Fiestr.Core.Interfaces;
using MediatR;
namespace Fiestr.Core.Aggregates.ProviderAggregate.Handlers;

public class ProviderImageDeletedHandler(IImageService imgService) : INotificationHandler<ProviderImageDeletedEvent>
{

  public async Task Handle(ProviderImageDeletedEvent e, CancellationToken ct)
  {
    await imgService.DeleteAsync(e.Image.ImageId, ct);
    await imgService.DeleteAsync(e.Image.ThumbnailId, ct);
  }
}
