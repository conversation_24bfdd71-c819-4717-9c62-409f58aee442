using Ardalis.GuardClauses;
using Ardalis.Result;
using Ardalis.SharedKernel;
using Fiestr.Core.Enums;
using Fiestr.SharedKernel;
namespace Fiestr.Core.Aggregates.ProductAggregate;

public class Product : EntityBase, IAggregateRoot
{
  private Product()
  {
  }

  public Product(int providerId, string name, string description, string? longDescription, string? sku, UnitType unitType, decimal unitPrice)
  {
    ProviderId = providerId;
    Sku = sku;
    Name = name;
    Description = description;
    LongDescription = longDescription;
    UnitType = unitType;
    UnitPrice = unitPrice;
    CreatedAt = DateTime.UtcNow;
  }

  public int ProviderId { get; private set; }

  public string Name { get; private set; } = null!;
  public string Description { get; private set; } = null!;
  public string? LongDescription { get; private set; }
  public string? Sku { get; private set; }
  public UnitType UnitType { get; private set; } 
  public decimal UnitPrice { get; private set; }
  public DateTime CreatedAt { get; private set; }
  
  private readonly List<ProductTags> _productTags = [];

  private readonly List<ProductImage> _images = [];
  public IReadOnlyCollection<ProductImage> Images => _images.AsReadOnly();
  public Result AddImage(string thumbnailId, string imageId)
  {
    if (_images.Count >= 5)
    {
      return Result.Invalid(new FiestrValidationError("You can only add 5 images to a product"));
    }
    var image = new ProductImage(thumbnailId, imageId);
    _images.Add(image);
    return Result.Success();
  }
  
  public void RemoveImage(ProductImage image)
  {
    _images.Remove(image);
  }
  
  // TAGS
  public IReadOnlyCollection<int> TagIds => _productTags.Select(e => e.TagId).ToList().AsReadOnly();
  public void SetTags(List<int> tagIds)
  {
    _productTags.Clear();
    var tags = tagIds.Select(tagId => new ProductTags()
    {
      TagId = tagId
    });
    _productTags.AddRange(tags);
  }
  
  public void RemoveTags(List<int> tagIds)
  {
    _productTags.RemoveAll(pt => tagIds.Contains(pt.TagId));
  }
}

public class ProductImage(string thumbnailId, string imageId)
{
  public string ThumbnailId { get; set; } = thumbnailId;
  public string ImageId { get; set; } = imageId;
}

public class ProductTags
{
  public int ProductId { get; set; }
  public int TagId { get; set; }
}
