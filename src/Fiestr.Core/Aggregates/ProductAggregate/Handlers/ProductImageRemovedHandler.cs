using Fiestr.Core.Aggregates.ProductAggregate.Events;
using Fiestr.Core.Interfaces;
using MediatR;
namespace Fiestr.Core.Aggregates.ProductAggregate.Handlers;

public class ProductImageRemovedHandler(IImageService imgService) : INotificationHandler<ProductImageRemovedEvent>
{
  public async Task Handle(ProductImageRemovedEvent e, CancellationToken ct)
  {
    await imgService.DeleteAsync(e.Image.ImageId, ct);
    await imgService.DeleteAsync(e.Image.ThumbnailId, ct);
  }
}
