using Ardalis.GuardClauses;
using Ardalis.SharedKernel;

namespace Fiestr.Core.Aggregates.ProviderReviewAggregate;

public class ProviderReview : IAggregateRoot
{
  private ProviderReview()
  {
  }
  
  public ProviderReview(int providerId, int score, string review)
  {
    Guard.Against.OutOfRange(score, nameof(score), 0, 100, "Score must be between 0 and 100");
    Guard.Against.InvalidInput(score, nameof(score), e => e % 5 == 0, "Score must be divisible by 5");
    ProviderId = providerId;
    Score = score;
    Review = review;
  }

  public int Id { get; private set; }
  public int ProviderId { get; private set; }
  public int Score { get; private set; }
  public string? Review { get; private set; }
}
