using System.Diagnostics.CodeAnalysis;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using Microsoft.IdentityModel.Tokens;
namespace Fiestr.Infrastructure.Security;

public class JwtHelper
{
  public static ClaimsPrincipal Parse(string token)
  {
    var handler = new JwtSecurityTokenHandler();
    var jwtToken = handler.ReadJwtToken(token);
    var claimsIdentity = new ClaimsIdentity(jwtToken.Claims, "JWT");
    var claimsPrincipal = new ClaimsPrincipal(claimsIdentity);
    return claimsPrincipal;
  }

  public static bool TryParse(string token, [NotNullWhen(true)] out ClaimsPrincipal? principal)
  {
    try
    {
      principal = Parse(token);
      return true;
    }
    catch (Exception)
    {
      principal = null;
      return false;
    }
  }

}
