using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using Ardalis.Result;
using Fiestr.Core;
using Fiestr.Core.Aggregates.UserAggregate;
using Fiestr.Core.Interfaces;
using Fiestr.Core.Models.Auth;
using Fiestr.Infrastructure.OktaRestClient;
using Fiestr.SharedKernel.Extensions;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
namespace Fiestr.Infrastructure.Security;

public class AuthService(IOktaClient oktaClient, IOptions<FiestrJwtConfiguration> jwtCfg) : IAuthService
{
  private readonly FiestrJwtConfiguration _jwtCfg = jwtCfg.Value;
  private static readonly string[] _defaultFiestrScopes =
  [
    Consts.JwtScopes.MyAccountManage
  ];
  
  private static readonly string[] _expectedOktaScopes =
  [
    Consts.OktaScopes.Email,
    Consts.OktaScopes.OpenId,
    Consts.OktaScopes.Profile,
  ];
  
  public async Task<Result<ExternalFederationAuthResult>> AuthorizeWithCode(string code, string redirectUri,
    CancellationToken ct)
  {
    var authResult = await oktaClient.AuthorizeWithCodeAsync(code, redirectUri, ct);
    if (!authResult.IsSuccess)
      authResult.MapToEmptyResult();
    
    if (!JwtHelper.TryParse(authResult.Value.AccessToken, out var claimsPrincipal))
      return Result.Error("Invalid token.");
    
    if (!claimsPrincipal.Claims.Any(e => e.Type == "scope" && e.Value.Split(' ').ContainsAll(_expectedOktaScopes)))
      return Result.Error($"Expected scopes {string.Join(", ", _expectedOktaScopes)} not found.");
    
    return await GetUserInfo(authResult.Value.AccessToken, ct);
  }
  
  public async Task<Result<ExternalFederationAuthResult>> GetUserInfo(string accessToken, CancellationToken ct)
  {
    if (!JwtHelper.TryParse(accessToken, out var claimsPrincipal))
      return Result.Error("Invalid token.");
    
    var userInfo = await oktaClient.GetUserInfoAsync(accessToken, ct);
    if (!userInfo.IsSuccess)
      return userInfo.MapToEmptyResult();
    
    return new ExternalFederationAuthResult
    {
      Claims = claimsPrincipal,
      Name = userInfo.Value.Name,
      Email = userInfo.Value.Email,
      Nickname = userInfo.Value.Nickname,
      UserId = userInfo.Value.Sub,
    };
  }

  public OAuthResponse OIdTokensForUser(User user)
  {
    var idToken = GenerateIdToken(user);
    var accessToken = GenerateAccessToken(user);

    var result = new OAuthResponse
    {
      Scope = string.Join(", ", _defaultFiestrScopes),
      AccessToken = accessToken,
      IdToken = idToken,
      ExpiresIn = _jwtCfg.Duration.TotalSeconds,
      TokenType = "Bearer"
    };
    return result;
  }
  
  private string GenerateIdToken(User user)
  {
    var claims = new[]
    {
      new Claim(Consts.JwtKeys.Sub, user.Id.ToString()),
      new Claim(Consts.JwtKeys.Name, user.Name),
      new Claim(Consts.JwtKeys.Nickname, user.Nickname),
      new Claim(Consts.JwtKeys.Email, user.Email),
      new Claim(Consts.JwtKeys.OktaUserId, user.OktaUserId)
    };

    return this.GenerateToken(claims);
  }
  
  private string GenerateAccessToken(User user)
  {
    var claims = new[]
    {
      new Claim(Consts.JwtKeys.Sub, user.Id.ToString()),
      new Claim(Consts.JwtKeys.Scope, "fiestr.myAccount.manage"),
    };

    return this.GenerateToken(claims);
  }

  private string GenerateToken(Claim[] claims)
  {
    var securityKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_jwtCfg.Key));
    var credentials = new SigningCredentials(securityKey, SecurityAlgorithms.HmacSha256);

    var token = new JwtSecurityToken(
      issuer: _jwtCfg.Issuer,
      audience: _jwtCfg.Audience,
      claims: claims,
      notBefore: DateTime.UtcNow,
      expires: DateTime.UtcNow.Add(_jwtCfg.Duration),
      signingCredentials: credentials);

    return new JwtSecurityTokenHandler().WriteToken(token);
  }

}
