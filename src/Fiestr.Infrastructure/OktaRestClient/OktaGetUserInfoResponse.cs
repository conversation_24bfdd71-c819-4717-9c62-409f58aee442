using System.Text.Json.Serialization;
namespace Fiestr.Infrastructure.OktaRestClient;

public class OktaGetUserInfoResponse
{
  [JsonPropertyName("sub")]
  public required string Sub { get; set; }

  [<PERSON>son<PERSON>ropertyName("nickname")]
  public required string Nickname { get; set; }

  [<PERSON>son<PERSON>ropertyName("name")]
  public required string Name { get; set; }

  [<PERSON>son<PERSON>ropertyName("picture")]
  public required string Picture { get; set; }

  [Json<PERSON>ropertyName("updated_at")]
  public required DateTime UpdatedAt { get; set; }

  [Json<PERSON>ropertyName("email")]
  public required string Email { get; set; }

  [Json<PERSON>ropertyName("email_verified")]
  public required bool EmailVerified { get; set; }
}
