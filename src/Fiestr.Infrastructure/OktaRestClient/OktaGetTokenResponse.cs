using System.Text.Json.Serialization;

namespace Fiestr.Infrastructure.OktaRestClient;

public class OktaGetTokenResponse
{
  [JsonPropertyName("access_token")]
  public required string AccessToken { get; set; }
  [JsonPropertyName("id_token")]
  public required string IdToken { get; set; }
  [JsonPropertyName("scope")]
  public required string Scope { get; set; }
  [JsonPropertyName("expires_in")]
  public required ulong ExpiresIn { get; set; }
  [JsonPropertyName("token_type")]
  public required string Bearer { get; set; }
}
