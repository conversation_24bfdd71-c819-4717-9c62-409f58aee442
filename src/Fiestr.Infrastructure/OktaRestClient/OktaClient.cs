using System.Net;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using Ardalis.Result;
using Fiestr.Infrastructure.Configuration;
using Microsoft.Extensions.Options;

namespace Fiestr.Infrastructure.OktaRestClient;

public class OktaClient : IOktaClient
{
  private readonly IOptions<OktaConfiguration> _cfg;
  private readonly HttpClient _client;
  private readonly Uri _baseUrl = new("https://fiestr.us.auth0.com");

  public OktaClient(IOptions<OktaConfiguration> cfg)
  {
    _cfg = cfg;
    var handler = new SocketsHttpHandler
    {
      PooledConnectionLifetime = TimeSpan.FromMinutes(15)
    };
    this._client = new HttpClient(handler);
  }

  public async Task<Result<OktaGetUserInfoResponse>> GetUserInfoAsync(string accessToken, CancellationToken ct = default)
  {
    var request = new HttpRequestMessage(HttpMethod.Get, new Uri(_baseUrl, "userinfo"));
    request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

    var response = await _client.SendAsync(request, ct);
    if (response.StatusCode == HttpStatusCode.Unauthorized)
      return Result.Unauthorized();
    
    var result = await response.Content.ReadFromJsonAsync<OktaGetUserInfoResponse>(cancellationToken: ct);
    // TODO log response
    if (result is null)
      return Result.CriticalError("Unexpected response from Okta UserInfo endpoint. Please contact an admin");
    return result;
  }

  public async Task<Result<OktaGetTokenResponse>> AuthorizeWithCodeAsync(string code, string redirectUri, CancellationToken ct)
  {
    // TODO maybe validate that it is in a specific domain
    var request = new HttpRequestMessage(HttpMethod.Post, new Uri(_baseUrl, "oauth/token"));
    var body = new
    {
      grant_type = "authorization_code",
      client_id = _cfg.Value.ClientId,
      client_secret = _cfg.Value.ClientSecret,
      code = code,
      redirect_uri = redirectUri
    };
    request.Content = JsonContent.Create(body);

    var response = await _client.SendAsync(request, ct);
    if (response.StatusCode == HttpStatusCode.Unauthorized)
      return Result.Unauthorized();
    
    var result = await response.Content.ReadFromJsonAsync<OktaGetTokenResponse>(cancellationToken: ct);
    // TODO log response
    if (result is null)
      return Result.CriticalError("Unexpected response from Okta UserInfo endpoint. Please contact an admin");
    return result;
  }
}
