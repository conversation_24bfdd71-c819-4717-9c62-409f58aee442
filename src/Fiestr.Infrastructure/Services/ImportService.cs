using System.IO.Compression;
using Dapper;
using Fiestr.Core.Aggregates.ProviderAggregate;
using Fiestr.Core.Enums;
using Fiestr.Infrastructure.Data;
using Fiestr.UseCases.Mgmt;
using Microsoft.EntityFrameworkCore;
using Microsoft.VisualBasic.FileIO;

namespace Fiestr.Infrastructure.Services;

public class ImportService(AppDbContext dbContext, IDbConnProvider conn): IImportService
{
  public void DeleteAll()
  {
    var tables = new[] { "Tags", "Provider", "ProviderImages", "ProviderTags", "Product", "ProductImages", "Image" };
    foreach (var table in tables)
    {
      dbContext.Database.GetDbConnection().Execute($"DELETE FROM {table}");
      dbContext.Database.GetDbConnection().Execute($"DELETE FROM SQLITE_SEQUENCE WHERE name= '{table}'");
    }
  }
  
  public void DeleteProvider(int providerId)
  {
    conn.GetDb().Open();
    using var txn = conn.GetDb().BeginTransaction();

    var mixedIds = conn.GetDb()
      .Query<(string, string)>($"select ImageId, ThumbnailId from ProviderImages where OwnerId = {providerId}");

    var ids = mixedIds.Select(e => new[] { e.Item1, e.Item2 }).SelectMany(e => e);

    foreach (var id in ids)
    {
      conn.GetDb().Execute($"delete from Image where id = '{id}' ");
    }
    conn.GetDb().Execute($"delete from ProviderImages where OwnerId = {providerId}");
    conn.GetDb().Execute($"delete from Provider where id = {providerId}");
    conn.GetDb().Execute($"delete from ProviderTags where ProviderId = {providerId}");
    txn.Commit();
  }
  public int ImportProvider(Stream zipStream)
  {
    using var archive = new ZipArchive(zipStream, ZipArchiveMode.Read);
    var productFileEntry = archive.Entries.FirstOrDefault(e => e.Name.Equals("Products.csv", StringComparison.OrdinalIgnoreCase));
    if (productFileEntry is null)
    {
      throw new FileNotFoundException("products.csv not found in the provided zip.");
    }
    
    var providerFileEntry = archive.Entries.FirstOrDefault(e => e.Name.Equals("ProviderMetadata.csv", StringComparison.OrdinalIgnoreCase));
    if (providerFileEntry is null)
    {
      throw new FileNotFoundException("ProviderMetadata.csv not found in the provided zip.");
    }

    conn.GetDb().Open();
    using var txn = conn.GetDb().BeginTransaction();
    var providerId = CreateProviders(providerFileEntry, archive);
    CreateProducts(providerId, productFileEntry, archive);
    txn.Commit();
    conn.GetDb().Close();

    return providerId;
  }
  public void ImportTags(Stream tagCsvStream)
  {
    using var parser = new TextFieldParser(tagCsvStream);
    parser.Delimiters = [","];
    parser.ReadFields();

    var tags = new List<TagImport>();

    while (!parser.EndOfData)
    {
      var fields = parser.ReadFields();

      var tag = new TagImport
      {
        Name = fields![0],
        Category = fields[1],
        Code = fields[2],
        EntityType = Enum.Parse<TagEntityType>(fields[3])
      };
      tags.Add(tag);
    }

    dbContext.Database.GetDbConnection().Execute(
      "INSERT INTO Tags (Name, Category, Code, EntityType) VALUES (@Name, @Category, @Code, @EntityType)",
      tags.Select(e => new
      {
        e.Name,
        e.Category,
        e.Code,
        e.EntityType
      }),
      conn.GetTxn());
  }

  
  private string SanitizeExcelMoney(string moneyInput)
  {
    if (moneyInput.StartsWith("$"))
      moneyInput = moneyInput[1..];

    if (moneyInput == "-")
      moneyInput = "0";
    
    return moneyInput;
  }

  private void CreateProducts(int providerId, ZipArchiveEntry productFileEntry, ZipArchive archive)
  {
    using var stream = productFileEntry.Open();
    using var reader = new StreamReader(stream);
    using var parser = new TextFieldParser(reader);
    parser.Delimiters = [","];
    parser.ReadFields();

    var parsed = new List<ImportProduct>();
    while (!parser.EndOfData)
    {
      var fields = parser.ReadFields();
      if (fields is null) continue;

      var product = new ImportProduct
      {
        Id = Convert.ToInt32(fields![0]),
        Name = fields[1],
        ImgPath = fields[2], // Image path reference
        Description = fields[3],
        LongDescription = fields[4],
        TagCodes = fields[5],
        ColorStyle = fields[6],
        UnitType = fields[7].ToUnitType(),
        UnitPrice = Convert.ToDecimal(SanitizeExcelMoney(fields[8])),
        UnitPrice2 = Convert.ToDecimal(SanitizeExcelMoney(fields[9])),
        Published = Convert.ToBoolean(fields[10])
      };
      parsed.Add(product);
    }

    const string productInsertSql =
      """
      insert into Product (ProviderId, Name, Description, LongDescription, Sku, UnitType, UnitPrice, CreatedAt)
      values (@ProviderId, @Name, @Description, @LongDescription, @Sku, @UnitType, @UnitPrice, @CreatedAt);
      select last_insert_rowid();
      """;

    foreach (var product in parsed)
    {
      var productId = conn.GetDb().ExecuteScalar<int>(productInsertSql, new
      {
        ProviderId = providerId,
        Name = product.Name,
        Description = product.Description,
        LongDescription = product.LongDescription,
        Sku = product.Id,
        UnitType = product.UnitType,
        UnitPrice = product.UnitPrice,
        CreatedAt = DateTimeOffset.Now.ToUnixTimeMilliseconds()
      }, conn.GetTxn());

      ImportProductImages(productId, product.ImgPath, archive);
      var tagCodes = product.TagCodes.Split(',', StringSplitOptions.RemoveEmptyEntries)
        .Select(tag => tag.Trim())
        .ToList();

      if (tagCodes.Any())
      {
        const string tagQuery =
          """
          select id from Tags where code in @TagCodes;
          """;

        var tagIds = conn.GetDb().Query<int>(tagQuery, new { TagCodes = tagCodes }, conn.GetTxn()).ToList();

        if (tagIds.Any())
        {
          const string productTagInsertSql =
            """
            insert into ProductTags (productId, tagId)
            values (@ProductId, @TagId);
            """;

          var productTagParams = tagIds.Select(tagId => new { ProductId = productId, TagId = tagId });
          conn.GetDb().Execute(productTagInsertSql, productTagParams, conn.GetTxn());
        }
      }
    }
  }
  
  private void ImportProductImages(int productId, string imgPath, ZipArchive archive)
  {
    var productImageFolder = archive.Entries
      .Where(e => 
        e.FullName.StartsWith($"ProductImages/{imgPath}/", StringComparison.OrdinalIgnoreCase)
        && e.Name.EndsWith(".webp", StringComparison.OrdinalIgnoreCase))
      .ToList();

    if (!productImageFolder.Any()) return;

    string insertImageSql = 
      """
      insert into Image (Id, Data, ContentType) 
      values (@Id, @Data, @ContentType);
      """;

    string insertProductImageSql =
      """
      insert into ProductImages (ThumbnailId, ImageId, OwnerId)
      values (@ThumbnailId, @ImageId, @OwnerId);
      """;

    string? thumbnailId = null;

    foreach (var entry in productImageFolder)
    {
      using var imageStream = entry.Open();
      using var memoryStream = new MemoryStream();
      imageStream.CopyTo(memoryStream);
      var imageData = memoryStream.ToArray();

      var imageId = Guid.NewGuid().ToString();
      var contentType = GetContentType(entry.Name);

      conn.GetDb().Execute(insertImageSql, new
      {
        Id = imageId,
        Data = imageData,
        ContentType = contentType
      }, conn.GetTxn());

      if (thumbnailId == null)
      {
        thumbnailId = imageId;
      }

      conn.GetDb().Execute(insertProductImageSql, new
      {
        ThumbnailId = thumbnailId,
        ImageId = imageId,
        OwnerId = productId
      }, conn.GetTxn());
    }
  }
  
  private string GetContentType(string fileName)
  {
    var extension = Path.GetExtension(fileName).ToLower();
    return extension switch
    {
      ".jpg" or ".jpeg" => "image/jpeg",
      ".png" => "image/png",
      ".gif" => "image/gif",
      ".webp" => "image/webp",
      _ => "application/octet-stream"
    };
  }
  
  private int CreateProviders(ZipArchiveEntry providerFileEntry, ZipArchive archive)
  {
    using var stream = providerFileEntry.Open();
    using var reader = new StreamReader(stream);
    using var parser = new TextFieldParser(reader);
    parser.Delimiters = [","];
    parser.ReadFields(); // Skip headers

    if (parser.EndOfData) throw new InvalidOperationException("Provider file is empty.");

    var fields = parser.ReadFields();
    if (fields is null || fields.Length < 4) throw new InvalidOperationException("Invalid provider file format.");

    var provider = new
    {
      Name = fields[0],
      PhoneNumber = fields[1],
      Address1 = fields[2],
      CreatedAt = DateTimeOffset.Now.ToUnixTimeMilliseconds()
    };

    const string providerInsertSql =
      """
      insert into Provider (name, phoneNumber, address1, createdAt)
      values (@Name, @PhoneNumber, @Address1, @CreatedAt);
      select last_insert_rowid();
      """;

    var providerId = conn.GetDb().ExecuteScalar<int>(providerInsertSql, provider, conn.GetTxn());

    var tagCodes = fields[3].Split(',', StringSplitOptions.RemoveEmptyEntries)
      .Select(tag => tag.Trim())
      .ToList();

    if (tagCodes.Any())
    {
      const string tagQuery =
        """
        select id from Tags where code in @TagCodes;
        """;

      var tagIds = conn.GetDb().Query<int>(tagQuery, new { TagCodes = tagCodes }, conn.GetTxn()).ToList();

      if (tagIds.Any())
      {
        const string providerTagInsertSql =
          """
          insert into ProviderTags (providerId, tagId)
          values (@ProviderId, @TagId);
          """;

        var providerTagParams = tagIds.Select(tagId => new { ProviderId = providerId, TagId = tagId });
        conn.GetDb().Execute(providerTagInsertSql, providerTagParams, conn.GetTxn());
      }
    }

    ImportProviderImages(providerId, archive);

    return providerId;
  }
  
  private void ImportProviderImages(int providerId, ZipArchive archive)
  {
    var providerImageFolder = archive.Entries
      .Where(e => 
        e.FullName.StartsWith("ProviderImages/", StringComparison.OrdinalIgnoreCase)
        && e.Name.EndsWith(".webp", StringComparison.OrdinalIgnoreCase)
        )
      .ToList();

    if (!providerImageFolder.Any()) return;

    string insertImageSql =
      """
      insert into Image (id, data, contentType) 
      values (@Id, @Data, @ContentType);
      """;

    string insertProviderImageSql =
      """
      insert into ProviderImages (thumbnailId, imageId, ownerId)
      values (@ThumbnailId, @ImageId, @OwnerId);
      """;


    foreach (var entry in providerImageFolder)
    {
      using var imageStream = entry.Open();
      using var memoryStream = new MemoryStream();
      imageStream.CopyTo(memoryStream);
      var imageData = memoryStream.ToArray();

      var imageId = Guid.NewGuid().ToString();
      var contentType = GetContentType(entry.Name);

      // If the name of the file is logo, disregarding extension, then it's the logo
      if (entry.Name.StartsWith("logo", StringComparison.OrdinalIgnoreCase))
      {
        const string updateLogoSql =
          """
          update Provider set Logo_ThumbnailId = @ThumbnailId, Logo_ImageId = @ImageId where id = @ProviderId;
          """;

        conn.GetDb().Execute(updateLogoSql, new
        {
          ThumbnailId = imageId,
          ImageId = imageId,
          ProviderId = providerId
        }, conn.GetTxn());
        continue;
      }
      
      // If the name of the file is logo, disregarding extension, then it's the logo
      if (entry.Name.StartsWith("banner", StringComparison.OrdinalIgnoreCase))
      {
        const string updateLogoSql =
          """
          update Provider set Logo_ThumbnailId = @ThumbnailId, Logo_ImageId = @ImageId where id = @ProviderId;
          """;

        conn.GetDb().Execute(updateLogoSql, new
        {
          ThumbnailId = imageId,
          ImageId = imageId,
          ProviderId = providerId
        }, conn.GetTxn());
        continue;
      }

      conn.GetDb().Execute(insertImageSql, new
      {
        Id = imageId,
        Data = imageData,
        ContentType = contentType
      }, conn.GetTxn());

      conn.GetDb().Execute(insertProviderImageSql, new
      {
        ThumbnailId = imageId,
        ImageId = imageId,
        OwnerId = providerId
      }, conn.GetTxn());
      
      
    }
  }


  public class TagImport
  {
    public string? Name { get; set; }
    public string? Category { get; set; }
    public string? Code { get; set; }
    public TagEntityType? EntityType { get; set; }
  }

  public class ImportProduct
  {
    public int Id { get; set; }
    public string Name { get; set; } = null!;
    public string ImgPath { get; set; } = null!;
    public string Description { get; set; } = null!;
    public string? LongDescription { get; set; }
    public string TagCodes { get; set; } = string.Empty;
    public string? ColorStyle { get; set; }
    public UnitType UnitType { get; set; }
    public decimal UnitPrice { get; set; }
    public decimal UnitPrice2 { get; set; }
    public bool Published { get; set; }
  }
}
