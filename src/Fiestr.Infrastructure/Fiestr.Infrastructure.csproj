<Project Sdk="Microsoft.NET.Sdk">
  <Sdk Name="Microsoft.Build.CentralPackageVersions" Version="2.1.3" />

  <ItemGroup>
    <PackageReference Include="Ardalis.SharedKernel" />
    <PackageReference Include="Ardalis.Specification.EntityFrameworkCore" />
    <PackageReference Include="Dapper" />
    <PackageReference Include="Dapper.SimpleSqlBuilder" />
    <PackageReference Include="LinqKit" />
    <PackageReference Include="Magick.NET-Q16-AnyCPU" />
    <PackageReference Include="Magick.NET.Core" />
    <PackageReference Include="MailKit" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" PrivateAssets="all" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" />
    <PackageReference Include="Microsoft.Extensions.Configuration" />
    <PackageReference Include="Microsoft.Extensions.Logging" />
    <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" />
    <PackageReference Include="SQLite" />
  </ItemGroup>
  
  <ItemGroup>
    <ProjectReference Include="..\Fiestr.Core\Fiestr.Core.csproj" />
    <ProjectReference Include="..\Fiestr.UseCases\Fiestr.UseCases.csproj" />
  </ItemGroup>
  
  <ItemGroup>
    <Folder Include="Context\" />
  </ItemGroup>
</Project>
