using Ardalis.SharedKernel;
using Fiestr.Core.Aggregates.ImageDetailAggregate;
using Fiestr.Core.Interfaces;
using Fiestr.Infrastructure.Data;
using Fiestr.SharedKernel.Extensions;
using ImageMagick;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
namespace Fiestr.Infrastructure.ImageProcessing;

public class ImageService(IRepository<ImageDefinition> imgRepo, AppDbContext dbContext, IOptions<ImageConfiguration> cfg) : IImageService
{
  public async Task<Stream> Resize(Stream imgStream, uint dimensionLimit, CancellationToken ct = default)
  {
    var img = new MagickImage(imgStream);
    if (img.Height >= dimensionLimit)
    {
      var size = new MagickGeometry(0, dimensionLimit);
      img.Resize(size);
      await img.WriteAsync(imgStream, ct);
    }
    else if (img.Width >= dimensionLimit)
    {
      var size = new MagickGeometry(dimensionLimit, 0);
      img.Resize(size);
      await img.WriteAsync(imgStream, ct);
    }
    return imgStream;
  }

  public Stream Compress(Stream imgStream)
  {
    var optimizer = new ImageOptimizer
    {
      OptimalCompression = cfg.Value.OptimalCompression
    };

    if (cfg.Value.LossyCompression)
      optimizer.Compress(imgStream);
    else
      optimizer.LosslessCompress(imgStream);

    imgStream.SeekStart();
    return imgStream;
  }

  public async Task<string> ProcessMainImageAsync(Stream imgStream, CancellationToken ct = default)
  {
    return await ProcessAndUploadAsync(imgStream, cfg.Value.ImageMaxMaxDimensions, ct);
  }

  public async Task<string> ProcessThumbnailAsync(Stream imgStream, CancellationToken ct = default)
  {
    return await ProcessAndUploadAsync(imgStream, cfg.Value.ThumbnailMaxDimensions, ct);
  }

  private async Task<string> ProcessAndUploadAsync(Stream imgStream, uint dimensionLimit, CancellationToken ct = default)
  {
    var stream = new MemoryStream();
    await imgStream.CopyToAsync(stream, ct);
    imgStream.SeekStart();
    stream.SeekStart();
    var resizedImage = await Resize(stream, dimensionLimit, ct);
    var compressedImage = Compress(resizedImage);
    var img = new MagickImage(compressedImage);
    var formatInfo = MagickFormatInfo.Create(img.Format);
    var entity = new ImageDefinition
    {
      ContentType = formatInfo!.MimeType, Data = img.ToByteArray()
    };
    await imgRepo.AddAsync(entity, ct);
    return entity.Id;
  }

  public async Task DeleteAsync(string imgId, CancellationToken ct = default)
  {
    await dbContext.Images.Where(e => e.Id == imgId).ExecuteDeleteAsync(ct);
  }
}
