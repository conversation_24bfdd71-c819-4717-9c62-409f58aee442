namespace Fiestr.Infrastructure.ImageProcessing;

public class ImageConfiguration
{
  public const string Position = "ImageConfiguration";
  public uint ThumbnailMaxDimensions { get; set; }
  public uint ImageMaxMaxDimensions { get; set; }
  public int MinBytes { get; set; }
  public int MaxBytes { get; set; }
  public List<string> SupportedFormats { get; set; } = [];
  public bool LossyCompression { get; set; }
  public bool OptimalCompression { get; set; }
}
