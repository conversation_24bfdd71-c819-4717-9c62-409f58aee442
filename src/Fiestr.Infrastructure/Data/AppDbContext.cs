using System.Reflection;
using Ardalis.SharedKernel;
using Fiestr.Core.Aggregates.ImageDetailAggregate;
using Fiestr.Core.Aggregates.ProviderAggregate;
using Fiestr.Core.Aggregates.UserAggregate;
using Microsoft.EntityFrameworkCore;

namespace Fiestr.Infrastructure.Data;
public class AppDbContext : DbContext
{
  private readonly IDomainEventDispatcher? _dispatcher;

  public AppDbContext(DbContextOptions<AppDbContext> options,
    IDomainEventDispatcher? dispatcher)
      : base(options)
  {
    _dispatcher = dispatcher;
  }

  public DbSet<Provider> Providers => Set<Provider>();
  public DbSet<User> Users => Set<User>();
  public DbSet<Tag> Tags => Set<Tag>();
  public DbSet<ImageDefinition> Images => Set<ImageDefinition>();

  protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
  {
    #if DEBUG
        optionsBuilder.EnableSensitiveDataLogging();
    #endif
    base.OnConfiguring(optionsBuilder);
  }

  protected override void OnModelCreating(ModelBuilder modelBuilder)
  {
    base.OnModelCreating(modelBuilder);
    modelBuilder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());
  }

  protected override void ConfigureConventions(ModelConfigurationBuilder configurationBuilder)
  {
    configurationBuilder.Properties<DateTime>().HaveConversion<DateTimeToEpochConverter>();
  }

  public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = new CancellationToken())
  {
    int result = await base.SaveChangesAsync(cancellationToken).ConfigureAwait(false);

    // ignore events if no dispatcher provided
    if (_dispatcher == null) return result;

    // dispatch events only if save was successful
    var entitiesWithEvents = ChangeTracker.Entries<EntityBase>()
        .Select(e => e.Entity)
        .Where(e => e.DomainEvents.Any())
        .ToArray();

    await _dispatcher.DispatchAndClearEvents(entitiesWithEvents);

    return result;
  }

  public override int SaveChanges() =>
        SaveChangesAsync().GetAwaiter().GetResult();
}
