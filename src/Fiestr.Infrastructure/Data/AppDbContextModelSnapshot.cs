// <auto-generated />
using System;
using Fiestr.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace Fiestr.Infrastructure.Data
{
    [DbContext(typeof(AppDbContext))]
    partial class AppDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder.HasAnnotation("ProductVersion", "9.0.5");

            modelBuilder.Entity("Fiestr.Core.Aggregates.ImageDetailAggregate.ImageDefinition", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<string>("ContentType")
                        .HasColumnType("TEXT");

                    b.Property<byte[]>("Data")
                        .HasColumnType("BLOB");

                    b.<PERSON>("Id");

                    b.ToTable("Image", (string)null);
                });

            modelBuilder.Entity("Fiestr.Core.Aggregates.ProductAggregate.Product", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<double>("CreatedAt")
                        .HasColumnType("REAL");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("LongDescription")
                        .HasMaxLength(10000)
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<int>("ProviderId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Sku")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("UnitPrice")
                        .HasColumnType("TEXT");

                    b.Property<int>("UnitType")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("ProviderId");

                    b.ToTable("Product");
                });

            modelBuilder.Entity("Fiestr.Core.Aggregates.ProductAggregate.ProductTags", b =>
                {
                    b.Property<int>("ProductId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("TagId")
                        .HasColumnType("INTEGER");

                    b.HasKey("ProductId", "TagId");

                    b.HasIndex("TagId");

                    b.ToTable("ProductTags");
                });

            modelBuilder.Entity("Fiestr.Core.Aggregates.ProviderAggregate.Provider", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Address1")
                        .IsRequired()
                        .HasMaxLength(3000)
                        .HasColumnType("TEXT");

                    b.Property<double>("CreatedAt")
                        .HasColumnType("REAL");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("PhoneNumber")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex(new[] { "CreatedAt" }, "IX_Provider_CreatedAt");

                    b.HasIndex(new[] { "Name" }, "IX_Provider_Name");

                    b.ToTable("Provider", (string)null);
                });

            modelBuilder.Entity("Fiestr.Core.Aggregates.ProviderAggregate.ProviderTags", b =>
                {
                    b.Property<int>("ProviderId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("TagId")
                        .HasColumnType("INTEGER");

                    b.HasKey("ProviderId", "TagId");

                    b.HasIndex("TagId");

                    b.ToTable("ProviderTags");
                });

            modelBuilder.Entity("Fiestr.Core.Aggregates.ProviderAggregate.ProviderUsers", b =>
                {
                    b.Property<int>("ProviderId")
                        .HasColumnType("INTEGER");

                    b.Property<Guid>("UserId")
                        .HasColumnType("TEXT");

                    b.HasKey("ProviderId", "UserId");

                    b.HasIndex("UserId");

                    b.ToTable("ProviderUsers");
                });

            modelBuilder.Entity("Fiestr.Core.Aggregates.ProviderAggregate.Tag", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Category")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<long>("EntityType")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("Tags");
                });

            modelBuilder.Entity("Fiestr.Core.Aggregates.ProviderReviewAggregate.ProviderReview", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int>("ProviderId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Review")
                        .HasMaxLength(2000)
                        .HasColumnType("TEXT");

                    b.Property<int>("Score")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("ProviderId");

                    b.ToTable("ProviderReview");
                });

            modelBuilder.Entity("Fiestr.Core.Aggregates.UserAggregate.User", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Nickname")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("OktaUserId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex(new[] { "OktaUserId" }, "IX_User_OktaUserId");

                    b.ToTable("Users");
                });

            modelBuilder.Entity("Fiestr.Core.Aggregates.ProductAggregate.Product", b =>
                {
                    b.HasOne("Fiestr.Core.Aggregates.ProviderAggregate.Provider", null)
                        .WithMany()
                        .HasForeignKey("ProviderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsMany("Fiestr.Core.Aggregates.ProductAggregate.ProductImage", "Images", b1 =>
                        {
                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("INTEGER");

                            b1.Property<string>("ImageId")
                                .IsRequired()
                                .HasColumnType("TEXT");

                            b1.Property<int>("OwnerId")
                                .HasColumnType("INTEGER");

                            b1.Property<string>("ThumbnailId")
                                .IsRequired()
                                .HasColumnType("TEXT");

                            b1.HasKey("Id");

                            b1.HasIndex("ImageId");

                            b1.HasIndex("OwnerId");

                            b1.HasIndex("ThumbnailId");

                            b1.ToTable("ProductImages", (string)null);

                            b1.HasOne("Fiestr.Core.Aggregates.ImageDetailAggregate.ImageDefinition", null)
                                .WithMany()
                                .HasForeignKey("ImageId")
                                .OnDelete(DeleteBehavior.Cascade)
                                .IsRequired();

                            b1.WithOwner()
                                .HasForeignKey("OwnerId");

                            b1.HasOne("Fiestr.Core.Aggregates.ImageDetailAggregate.ImageDefinition", null)
                                .WithMany()
                                .HasForeignKey("ThumbnailId")
                                .OnDelete(DeleteBehavior.Cascade)
                                .IsRequired();
                        });

                    b.Navigation("Images");
                });

            modelBuilder.Entity("Fiestr.Core.Aggregates.ProductAggregate.ProductTags", b =>
                {
                    b.HasOne("Fiestr.Core.Aggregates.ProductAggregate.Product", null)
                        .WithMany("_productTags")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Fiestr.Core.Aggregates.ProviderAggregate.Tag", null)
                        .WithMany()
                        .HasForeignKey("TagId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Fiestr.Core.Aggregates.ProviderAggregate.Provider", b =>
                {
                    b.OwnsOne("Fiestr.Core.Aggregates.ProviderAggregate.ProviderImage", "Logo", b1 =>
                        {
                            b1.Property<int>("Id")
                                .HasColumnType("INTEGER");

                            b1.Property<string>("ImageId")
                                .IsRequired()
                                .HasColumnType("TEXT");

                            b1.Property<string>("ThumbnailId")
                                .IsRequired()
                                .HasColumnType("TEXT");

                            b1.HasKey("Id");

                            b1.HasIndex("ImageId");

                            b1.HasIndex("ThumbnailId");

                            b1.ToTable("Provider");

                            b1.WithOwner()
                                .HasForeignKey("Id");

                            b1.HasOne("Fiestr.Core.Aggregates.ImageDetailAggregate.ImageDefinition", null)
                                .WithMany()
                                .HasForeignKey("ImageId")
                                .OnDelete(DeleteBehavior.Cascade)
                                .IsRequired();

                            b1.HasOne("Fiestr.Core.Aggregates.ImageDetailAggregate.ImageDefinition", null)
                                .WithMany()
                                .HasForeignKey("ThumbnailId")
                                .OnDelete(DeleteBehavior.Cascade)
                                .IsRequired();
                        });

                    b.OwnsMany("Fiestr.Core.Aggregates.ProviderAggregate.ProviderImage", "Images", b1 =>
                        {
                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("INTEGER");

                            b1.Property<string>("ImageId")
                                .IsRequired()
                                .HasColumnType("TEXT");

                            b1.Property<int>("OwnerId")
                                .HasColumnType("INTEGER");

                            b1.Property<string>("ThumbnailId")
                                .IsRequired()
                                .HasColumnType("TEXT");

                            b1.HasKey("Id");

                            b1.HasIndex("ImageId");

                            b1.HasIndex("OwnerId");

                            b1.HasIndex("ThumbnailId");

                            b1.ToTable("ProviderImages", (string)null);

                            b1.HasOne("Fiestr.Core.Aggregates.ImageDetailAggregate.ImageDefinition", null)
                                .WithMany()
                                .HasForeignKey("ImageId")
                                .OnDelete(DeleteBehavior.Cascade)
                                .IsRequired();

                            b1.WithOwner()
                                .HasForeignKey("OwnerId");

                            b1.HasOne("Fiestr.Core.Aggregates.ImageDetailAggregate.ImageDefinition", null)
                                .WithMany()
                                .HasForeignKey("ThumbnailId")
                                .OnDelete(DeleteBehavior.Cascade)
                                .IsRequired();
                        });

                    b.Navigation("Images");

                    b.Navigation("Logo");
                });

            modelBuilder.Entity("Fiestr.Core.Aggregates.ProviderAggregate.ProviderTags", b =>
                {
                    b.HasOne("Fiestr.Core.Aggregates.ProviderAggregate.Provider", null)
                        .WithMany("_providerTags")
                        .HasForeignKey("ProviderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Fiestr.Core.Aggregates.ProviderAggregate.Tag", null)
                        .WithMany()
                        .HasForeignKey("TagId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Fiestr.Core.Aggregates.ProviderAggregate.ProviderUsers", b =>
                {
                    b.HasOne("Fiestr.Core.Aggregates.ProviderAggregate.Provider", null)
                        .WithMany("_providerUsers")
                        .HasForeignKey("ProviderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Fiestr.Core.Aggregates.UserAggregate.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Fiestr.Core.Aggregates.ProviderReviewAggregate.ProviderReview", b =>
                {
                    b.HasOne("Fiestr.Core.Aggregates.ProviderAggregate.Provider", null)
                        .WithMany()
                        .HasForeignKey("ProviderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Fiestr.Core.Aggregates.ProductAggregate.Product", b =>
                {
                    b.Navigation("_productTags");
                });

            modelBuilder.Entity("Fiestr.Core.Aggregates.ProviderAggregate.Provider", b =>
                {
                    b.Navigation("_providerTags");

                    b.Navigation("_providerUsers");
                });
#pragma warning restore 612, 618
        }
    }
}
