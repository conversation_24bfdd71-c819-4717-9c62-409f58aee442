using Ardalis.SharedKernel;
using Ardalis.Specification.EntityFrameworkCore;
using Fiestr.Core.Interfaces;
using Microsoft.EntityFrameworkCore.Storage;

namespace Fiestr.Infrastructure.Data;

// inherit from Ardalis.Specification type
public class EfRepository<T> : RepositoryBase<T>, IReadRepository<T>, IRepository<T> where T : class, IAggregateRoot
{
  public EfRepository(AppDbContext dbContext) : base(dbContext)
  {
  }
}

public class UnitOfWork : IUnitOfWork
{
  private readonly AppDbContext _context;
  private IDbContextTransaction? _transaction;

  public UnitOfWork(AppDbContext context)
  {
    _context = context;
  }

  public void BeginTransaction()
  {
    if (_transaction != null)
    {
      return;
    }

    _transaction = _context.Database.BeginTransaction();
  }

  public Task<int> SaveChangesAsync()
  {
    return _context.SaveChangesAsync();
  }

  public void Commit()
  {
    if (_transaction == null)
    {
      return;
    }

    _transaction.Commit();
    _transaction.Dispose();
    _transaction = null;
  }

  public async Task SaveAndCommitAsync()
  {
    await SaveChangesAsync();
    Commit();
  }

  public void Rollback()
  {
    if (_transaction == null)
    {
      return;
    }

    _transaction.Rollback();
    _transaction.Dispose();
    _transaction = null;
  }

  public void Dispose()
  {
    if (_transaction == null)
    {
      return;
    }

    _transaction.Dispose();
    _transaction = null;
  }
}


