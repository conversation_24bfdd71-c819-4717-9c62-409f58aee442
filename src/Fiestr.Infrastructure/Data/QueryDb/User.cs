namespace Fiestr.Infrastructure.Data.QueryDb;

public partial class User
{
    public string Id { get; set; } = null!;

    public string OktaUserId { get; set; } = null!;

    public string Name { get; set; } = null!;

    public string Nickname { get; set; } = null!;

    public string Email { get; set; } = null!;

    public virtual ICollection<Provider> Providers { get; set; } = new List<Provider>();
}
