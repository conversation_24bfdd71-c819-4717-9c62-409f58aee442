using Fiestr.Core.Enums;

namespace Fiestr.Infrastructure.Data.QueryDb;

public partial class Product
{
    public int Id { get; set; }

    public int ProviderId { get; set; }

    public string Name { get; set; } = null!;

    public string Description { get; set; } = null!;

    public string? LongDescription { get; set; }

    public string? Sku { get; set; }

    public UnitType UnitType { get; set; }

    public decimal UnitPrice { get; set; }

    public DateTime CreatedAt { get; set; }

    public virtual ICollection<ProductImage> ProductImages { get; set; } = new List<ProductImage>();

    public virtual Provider Provider { get; set; } = null!;

    public virtual ICollection<Tag> Tags { get; set; } = new List<Tag>();
}
