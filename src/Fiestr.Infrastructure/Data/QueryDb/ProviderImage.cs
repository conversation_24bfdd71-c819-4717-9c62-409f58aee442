namespace Fiestr.Infrastructure.Data.QueryDb;

public partial class ProviderImage
{
    public int Id { get; set; }

    public string ThumbnailId { get; set; } = null!;

    public string ImageId { get; set; } = null!;

    public int OwnerId { get; set; }

    public virtual Image Image { get; set; } = null!;

    public virtual Provider Owner { get; set; } = null!;

    public virtual Image Thumbnail { get; set; } = null!;
}
