namespace Fiestr.Infrastructure.Data.QueryDb;

public partial class Tag
{
    public int Id { get; set; }

    public string Name { get; set; } = null!;

    public string Category { get; set; } = null!;

    public string Code { get; set; } = null!;

    public int EntityType { get; set; }

    public virtual ICollection<Product> Products { get; set; } = new List<Product>();

    public virtual ICollection<Provider> Providers { get; set; } = new List<Provider>();
}
