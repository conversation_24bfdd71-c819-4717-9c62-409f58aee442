using Fiestr.Core.Aggregates.ProviderAggregate;
using Fiestr.Core.Aggregates.UserAggregate;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
namespace Fiestr.Infrastructure.Data.Config;

public class ProviderUsersConfiguration : IEntityTypeConfiguration<ProviderUsers>
{
  public void Configure(EntityTypeBuilder<ProviderUsers> builder)
  {
    builder.Has<PERSON>ey(e => new
    {
      e.ProviderId, e.UserId
    });
    
    builder.HasOne<User>()
      .WithMany()
      .HasForeignKey(pu => pu.UserId)
      .OnDelete(DeleteBehavior.Cascade);
    
    builder.HasOne<Provider>()
      .WithMany("_providerUsers")
      .HasForeignKey(pu => pu.ProviderId)
      .OnDelete(DeleteBehavior.Cascade);
  }
}
