using Fiestr.Core.Aggregates.UserAggregate;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
namespace Fiestr.Infrastructure.Data.Config;

public class UserConfiguration : IEntityTypeConfiguration<User>
{
  public void Configure(EntityTypeBuilder<User> builder)
  {
    builder.Property(p => p.Name)
      .IsRequired();
    
    builder.Property(p => p.OktaUserId)
      .IsRequired();
    
    builder.Property(p => p.Email)
      .IsRequired();
    
    builder.Property(p => p.Nickname)
      .IsRequired();

    builder.HasIndex(e => e.<PERSON>Id, "IX_User_OktaUserId");
  }
}
