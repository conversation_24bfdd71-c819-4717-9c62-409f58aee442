using Fiestr.Core.Aggregates.ProductAggregate;
using Fiestr.Core.Aggregates.ProviderAggregate;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
namespace Fiestr.Infrastructure.Data.Config;

public class ProductTagsConfiguration : IEntityTypeConfiguration<ProductTags>
{
  public void Configure(EntityTypeBuilder<ProductTags> builder)
  {
    builder.HasKey(e => new
    {
      e.ProductId, e.TagId
    });
    
    builder.HasOne<Tag>()
      .WithMany()
      .HasForeignKey(pt => pt.TagId)
      .OnDelete(DeleteBehavior.Cascade);
    
    builder.HasOne<Product>()
      .WithMany("_productTags")
      .HasForeignKey(pt => pt.ProductId)
      .OnDelete(DeleteBehavior.Cascade);
  }
}
