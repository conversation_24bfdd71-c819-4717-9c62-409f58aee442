using Fiestr.Core.Aggregates.ImageDetailAggregate;
using Fiestr.Core.Aggregates.ProviderAggregate;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Fiestr.Infrastructure.Data.Config;

public class ProviderConfiguration : IEntityTypeConfiguration<Provider>
{
  public void Configure(EntityTypeBuilder<Provider> builder)
  {
    // PK
    builder.ToTable("Provider");
    builder.Property(p => p.Name)
        .HasMaxLength(DataSchemaConstants.DEFAULT_NAME_LENGTH)
        .IsRequired();

    builder.Property(e => e.Address1)
      .HasMaxLength(DataSchemaConstants.DEFAULT_ADDRESS_LENGTH);
    
    builder.OwnsOne<ProviderImage>(e => e.Logo, a =>
    {
      a.Property<int>("Id");
      a.<PERSON>("Id");
      a.<PERSON>ne<ImageDefinition>()
        .WithMany()
        .HasForeignKey(e => e.ImageId)
        .HasPrincipalKey(e => e.Id)
        .OnDelete(DeleteBehavior.Cascade);
      a.<PERSON>One<ImageDefinition>()
        .WithMany()
        .HasForeignKey(e => e.ThumbnailId)
        .HasPrincipalKey(e => e.Id)
        .OnDelete(DeleteBehavior.Cascade);
    });

    builder.OwnsOne<ProviderImage>(e => e.Banner, a =>
    {
      a.Property<int>("Id");
      a.HasKey("Id");
      a.HasOne<ImageDefinition>()
        .WithMany()
        .HasForeignKey(e => e.ImageId)
        .HasPrincipalKey(e => e.Id)
        .OnDelete(DeleteBehavior.Cascade);
      a.HasOne<ImageDefinition>()
        .WithMany()
        .HasForeignKey(e => e.ThumbnailId)
        .HasPrincipalKey(e => e.Id)
        .OnDelete(DeleteBehavior.Cascade);
    });
    
    // FKS
    builder
      .OwnsMany(e => e.Images, a =>
      {
        a.ToTable("ProviderImages");
        a.WithOwner().HasForeignKey("OwnerId");
        a.Property<int>("Id");
        a.HasKey("Id");
        a.HasOne<ImageDefinition>()
          .WithMany()
          .HasForeignKey(e => e.ImageId)
          .HasPrincipalKey(e => e.Id)
          .OnDelete(DeleteBehavior.Cascade);
        a.HasOne<ImageDefinition>()
          .WithMany()
          .HasForeignKey(e => e.ThumbnailId)
          .HasPrincipalKey(e => e.Id)
          .OnDelete(DeleteBehavior.Cascade);
      });
    
    // DF
    
    // IX
    builder.HasIndex(e => e.Name, "IX_Provider_Name");
    builder.HasIndex(e => e.CreatedAt, "IX_Provider_CreatedAt");
  }
}
