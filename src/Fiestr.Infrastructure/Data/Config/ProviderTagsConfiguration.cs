using Fiestr.Core.Aggregates.ProviderAggregate;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
namespace Fiestr.Infrastructure.Data.Config;

public class ProviderTagsConfiguration : IEntityTypeConfiguration<ProviderTags>
{
  public void Configure(EntityTypeBuilder<ProviderTags> builder)
  {
    builder.HasKey(e => new
    {
      e.ProviderId, e.TagId
    });
    
    builder.HasOne<Tag>()
      .WithMany()
      .HasForeignKey(pt => pt.TagId)
      .OnDelete(DeleteBehavior.Cascade);
    
    builder.HasOne<Provider>()
      .WithMany("_providerTags")
      .HasForeignKey(pt => pt.ProviderId)
      .OnDelete(DeleteBehavior.Cascade);
  }
}
