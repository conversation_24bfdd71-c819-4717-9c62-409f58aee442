using Fiestr.Core.Aggregates.ProviderAggregate;
using Fiestr.Core.Aggregates.ProviderReviewAggregate;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Fiestr.Infrastructure.Data.Config;

public class ProviderReviewConfiguration : IEntityTypeConfiguration<ProviderReview>
{
  public void Configure(EntityTypeBuilder<ProviderReview> builder)
  {
    builder.Property(p => p.Review)
      .HasMaxLength(DataSchemaConstants.DEFAULT_REVIEW_LENGTH);

    builder.HasOne<Provider>()
      .WithMany()
      .HasPrincipalKey(e => e.Id)
      .HasForeignKey(e => e.ProviderId)
      .OnDelete(DeleteBehavior.Cascade);
  }
}
