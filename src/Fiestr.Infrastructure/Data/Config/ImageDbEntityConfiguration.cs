using Fiestr.Core.Aggregates.ImageDetailAggregate;
using Fiestr.Core.Aggregates.ProviderAggregate;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore.ValueGeneration;
namespace Fiestr.Infrastructure.Data.Config;

public class ImageDbEntityConfiguration : IEntityTypeConfiguration<ImageDefinition>
{
  public void Configure(EntityTypeBuilder<ImageDefinition> builder)
  {
    builder.ToTable("Image");
    builder.HasKey(e => e.Id);

    builder.Property(p => p.Id)
      .HasValueGenerator<StringKeyGen>();
  }
}

public class StringKeyGen : ValueGenerator
{
  protected override object? NextValue(EntityEntry entry)
  {
      var random = new Random();
      const string chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
      return new string(Enumerable.Repeat(chars, 11)
        .Select(s => s[random.Next(s.Length)]).ToArray());
  }
  public override bool GeneratesTemporaryValues { get; } = false;
}
