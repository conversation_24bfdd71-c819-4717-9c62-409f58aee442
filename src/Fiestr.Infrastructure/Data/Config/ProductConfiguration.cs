using Fiestr.Core.Aggregates.ImageDetailAggregate;
using Fiestr.Core.Aggregates.ProductAggregate;
using Fiestr.Core.Aggregates.ProviderAggregate;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
namespace Fiestr.Infrastructure.Data.Config;

public class ProductConfiguration : IEntityTypeConfiguration<Product>
{
  public void Configure(EntityTypeBuilder<Product> builder)
  {
    builder.Property(p => p.Name)
      .HasMaxLength(DataSchemaConstants.DEFAULT_NAME_LENGTH)
      .IsRequired();

    builder.Property(p => p.Description)
      .HasMaxLength(DataSchemaConstants.DEFAULT_DESCRIPTION_LENGTH);
    
    builder.Property(p => p.LongDescription)
      .HasMaxLength(DataSchemaConstants.DEFAULT_LONG_DESCRIPTION_LENGTH);
    
    builder.Property(p => p.Sku)
      .HasMaxLength(DataSchemaConstants.DEFAULT_SKU_LENGTH);

    builder
      .OwnsMany(e => e.Images, a =>
      {
        a.ToTable("ProductImages");
        a.WithOwner().HasForeignKey("OwnerId");
        a.Property<int>("Id");
        a.HasKey("Id");
        a.HasOne<ImageDefinition>()
          .WithMany()
          .HasForeignKey(e => e.ImageId)
          .HasPrincipalKey(e => e.Id)
          .OnDelete(DeleteBehavior.Cascade);
        a.HasOne<ImageDefinition>()
          .WithMany()
          .HasForeignKey(e => e.ThumbnailId)
          .HasPrincipalKey(e => e.Id)
          .OnDelete(DeleteBehavior.Cascade);
      });

    builder.HasOne<Provider>()
      .WithMany()
      .HasForeignKey(e => e.ProviderId)
      .HasPrincipalKey(e => e.Id)
      .IsRequired();
  }
}
