using Fiestr.Infrastructure.Data.QueryDb;
using Fiestr.UseCases.GeneralModels;
using Fiestr.UseCases.Products;
using Fiestr.UseCases.Providers;

namespace Fiestr.Infrastructure.Data.Mappings;

public static partial class ProjectionExtensions
{
  public static IQueryable<ProviderSearchDto> ProviderSearch(this IQueryable<Provider> q) =>
    ProviderSearchProjection.Instance.Project(q);
}


public sealed class ProviderSearchProjection
  : ProjectionBase<Provider, ProviderSearchDto>
{
  public static readonly ProviderSearchProjection Instance = new();

  private ProviderSearchProjection() : base(
    p => new ProviderSearchDto
    {
      Id = p.Id,
      Address1 = p.Address1,
      Name = p.Name,
      PhoneNumber = p.PhoneNumber,
      TagIds = p.Tags.Select(t => t.Id).ToList(),
      Images = p.ProviderImages.Select(i => new ImageReferenceDto(i.ImageId, i.ThumbnailId)).ToList(),
      Rating = (int)p.ProviderReviews.Average(e => e.Score),
      RatingCount = p.ProviderReviews.Count,
      CreatedAt = p.CreatedAt,
      Logo = p.LogoImageId != null ? new ImageReferenceDto(p.LogoImageId!, p.LogoThumbnailId!) : null,
      Banner = p.BannerImageId != null ? new ImageReferenceDto(p.BannerImageId!, p.BannerThumbnailId!) : null
    }) {}
}
