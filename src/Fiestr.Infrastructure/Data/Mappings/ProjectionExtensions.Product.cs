using Fiestr.Infrastructure.Data.QueryDb;
using Fiestr.UseCases.GeneralModels;
using Fiestr.UseCases.Products;

namespace Fiestr.Infrastructure.Data.Mappings;

public static partial class ProjectionExtensions
{
  public static IQueryable<ProductDto> ProjectFull(this IQueryable<Product> q) =>
    ProductFullProjection.Instance.Project(q);
}

public sealed class ProductFullProjection
  : ProjectionBase<Product, ProductDto>
{
  public static readonly ProductFullProjection Instance = new();

  private ProductFullProjection() : base(
    p => new ProductDto
    {
      Id          = p.Id,
      Name        = p.Name,
      Description = p.Description,
      UnitPrice   = p.UnitPrice,
      UnitType    = p.UnitType,
      ProviderId  = p.ProviderId,
      Images      = p.ProductImages
        .Select(i => new ImageReferenceDto(i.ImageId, i.ThumbnailId))
        .ToList(),
      TagIds      = p.Tags.Select(t => t.Id).ToList(),
      LongDescription = p.LongDescription,
      CreatedAt   = p.CreatedAt
    }) {}
}
