using System.Linq.Expressions;

namespace Fiestr.Infrastructure.Data.Mappings;

public abstract class ProjectionBase<TEntity, TDto>(Expression<Func<TEntity, TDto>> expr)
{
  public Expression<Func<TEntity, TDto>> Expression { get; } = expr;

  // private readonly Func<TEntity, TDto> _func = expr.Compile();

  // --- helpers -----------------------------------------------------------

  public IQueryable<TDto> Project(IQueryable<TEntity> q) => q.Select(Expression);
  // public TDto Map(TEntity entity)                       => _func(entity);
}
