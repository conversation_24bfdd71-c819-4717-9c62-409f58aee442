using System.Linq.Expressions;
using Ardalis.Result;
using Fiestr.Infrastructure.Data.Mappings;
using Fiestr.Infrastructure.Data.QueryDb;
using Fiestr.UseCases.GeneralModels;
using Fiestr.UseCases.Products;
using Microsoft.EntityFrameworkCore;

namespace Fiestr.Infrastructure.Data.Queries;

public class ProductQueryServices(QueryDbContext dbContext) : IListProductsForProviderQueryService, IGetProductByIdQueryService
{
  public List<ProductDto> List(int providerId)
  {
    var results = dbContext.Products
      .AsNoTracking()
      .AsSplitQuery()
      .Where(e => e.ProviderId == providerId)
      .ProjectFull()
      .ToList();
    return results;
  }

  public Result<ProductDto> Get(int productId)
  {
    var qResult = dbContext.Products
      .AsNoTracking()
      .AsSplitQuery()
      .Where(e => e.Id == productId)
      .ProjectFull()
      .FirstOrDefault();
    return qResult is null ? Result.NotFound() : Result.Success(qResult);
  }
}

