using Dapper;
using Fiestr.UseCases.Providers.Create;
namespace Fiestr.Infrastructure.Data.Queries;
public class ProviderIdsForUserQueryService(IDbConnProvider connProvider) : IProviderIdsForUserQueryService
{
  public async Task<List<int>> ListAsync(Guid userId, CancellationToken ct = default)
  {
    var conn = connProvider.GetDb();
    var command = new CommandDefinition("select ProviderId from ProviderUsers where UserId = @userId", new
    {
      UserId = userId
    }, cancellationToken: ct);
    var qResult = await conn.QueryAsync<int>(command);
    var result = qResult.ToList();
    return result;
  }
}
