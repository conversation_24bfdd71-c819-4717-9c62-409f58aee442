using Dapper;
using Fiestr.UseCases.Tags;
using Fiestr.UseCases.Tags.List;
namespace Fiestr.Infrastructure.Data.Queries;

public class ListTagsQueryService(IDbConnProvider dbConnProvider): IListTagsQueryService
{
  public async Task<List<TagDto>> ListAsync(CancellationToken ct)
  {
    const string sql = @"SELECT t.Id, t.EntityType, t.Category, t.Code, t.Name FROM Tags t";
    var command = new CommandDefinition(sql, cancellationToken: ct);
    var qResult = await dbConnProvider.GetDb().QueryAsync<TagDto>(command);
    var result = qResult.ToList();
    return result;
  }
}
