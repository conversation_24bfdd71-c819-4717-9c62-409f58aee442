using System.Data;
using Ardalis.Result;
using Dapper;
using Fiestr.Infrastructure.Configuration;
using Fiestr.UseCases.ImageDefinitions;
using Fiestr.UseCases.ImageDefinitions.QueryById;
using Microsoft.Data.Sqlite;
using Microsoft.Extensions.Options;

namespace Fiestr.Infrastructure.Data.Queries;

public class ImageDefinitionByIdQueryService(IOptions<ConnectionStringConfiguration> cfg) : IImageDefinitionByIdQueryService
{
  public async Task<Result<ImageDefinitionDto>> GetAsync(string id, CancellationToken cancellationToken = default)
  {
    var sql = "select Id, Data, ContentType from Image where Id = @Id";
    await using var conn = new SqliteConnection(cfg.Value.SqliteConnection);
    var command = new CommandDefinition(
      commandText: sql,
      parameters: new { Id = id },
      transaction: null,
      commandType: CommandType.Text,
      cancellationToken: cancellationToken
    );
    var result = await conn.QueryFirstOrDefaultAsync<ImageDefinitionDto>(command);
    return result is null ? Result.NotFound() : Result.Success(result);
  }
}


