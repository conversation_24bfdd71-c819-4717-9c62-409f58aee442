using Dapper;
using Fiestr.Core.Models.Auth;
namespace Fiestr.Infrastructure.Data.Queries;

public class CustomClaimsQueryService(IDbConnProvider connProvider) : ICustomClaimsQueryService
{
  public async Task<CustomClaimsData> ListAsync(Guid userId, CancellationToken ct)
  {
    var conn = connProvider.GetDb();
    const string sql = "select ProviderId from ProviderUsers where UserId = @UserId";
    var command = new CommandDefinition(sql, new
    {
      UserId = userId
    }, cancellationToken: ct);
    
    var result = await conn.QueryAsync<int>(command);
    return new CustomClaimsData
    {
      LinkedProviderIds = result.ToList()
    };
  }
}
