using Dapper;
using Fiestr.UseCases.Products.AddImage;
using Microsoft.Data.Sqlite;
namespace Fiestr.Infrastructure.Data.Queries;

public class UserCanEditProductQueryService(IDbConnProvider dbProvider): IUserCanEditProductQueryService
{
  public async Task<bool> UserCanEditProduct(Guid userId, int productId, CancellationToken ct = default)
  {
    const string sql =
      """
      select 1
      from ProviderUsers providerUser
        join Product product on product.ProviderId = providerUser.ProviderId
      where
        providerUser.UserId = @UserId
        and product.Id = @ProductId
      """;
    var command = new CommandDefinition(sql, new
    {
      UserId = userId,
      ProductId = productId
    }, cancellationToken: ct);
    
    await using var conn = new SqliteConnection(dbProvider.GetConnStr());
    await conn.OpenAsync(ct);
    var result = await conn.QuerySingleOrDefaultAsync<int?>(command);
    return result.HasValue;
  }
}
