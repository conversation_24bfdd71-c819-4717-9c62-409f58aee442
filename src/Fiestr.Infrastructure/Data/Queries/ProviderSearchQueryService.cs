using System.Linq.Expressions;
using Ardalis.GuardClauses;
using Ardalis.Result;
using Fiestr.Infrastructure.Data.Mappings;
using Fiestr.Infrastructure.Data.QueryDb;
using Fiestr.UseCases.Providers;
using Fiestr.UseCases.Providers.Search;
using LinqKit;
using Microsoft.EntityFrameworkCore;

namespace Fiestr.Infrastructure.Data.Queries;

public class ProviderSearchQueryService(QueryDbContext dbContext) : IProviderSearchQueryService
{
  private static HashSet<string> _allowedOrderBy = ["name", "id", "createdAt"]; 
  private static HashSet<string> _allowedOrderType = ["asc", "desc"]; 
  public PagedResult<List<ProviderSearchDto>> List(ProviderSearchQuery query)
  {
    var predicate = PredicateBuilder.New<Provider>(true);
    if (query.Id is not null)
      predicate = predicate.And(e => e.Id == query.Id);
    
    if (query.TagIds.Count != 0)
      // Has any tags
      // predicate = predicate.And(e => e.Tags.Any(t => query.TagIds.Contains(t.Id)));
      // Has all tags
      predicate = predicate.And(e => query.TagIds.All(qt => e.Tags.Any(t => t.Id == qt)));
    
    if (!string.IsNullOrWhiteSpace(query.SearchTerm))
      predicate = predicate.And(e => EF.Functions.Like(e.Name, $"%{query.SearchTerm}%"));

    if (query.MinRating is not null)
      predicate = predicate.And(e => e.ProviderReviews.Average(pr => pr.Score) >= query.MinRating);
    
    if (query.MaxRating is not null)
      predicate = predicate.And(e => e.ProviderReviews.Average(pr => pr.Score) <= query.MaxRating);

    var queryable = dbContext.Providers
      .AsNoTracking()
      .AsSplitQuery()
      .Include(e => e.Tags)
      .Include(e => e.ProviderImages)
      .Where(predicate);

    if (!string.IsNullOrEmpty(query.OrderBy))
    {
      // TODO this is just to get it fast
      Guard.Against.NotFound(query.OrderBy, _allowedOrderBy);
      if (!string.IsNullOrEmpty(query.OrderType))
        Guard.Against.NotFound(query.OrderType, _allowedOrderType);
      
      Expression<Func<Provider, Object>> orderByFunc = p => p.Name;
      
      if (query.OrderBy.Equals("Id", StringComparison.OrdinalIgnoreCase))
        orderByFunc = p => p.Id;
      
      if (query.OrderBy.Equals("Name", StringComparison.OrdinalIgnoreCase))
        orderByFunc = p => p.Name;

      if (query.OrderBy.Equals("CreatedAt", StringComparison.OrdinalIgnoreCase))
        orderByFunc = p => p.CreatedAt;

      queryable = query.OrderType == "desc" 
        ? queryable.OrderByDescending(orderByFunc) 
        : queryable.OrderBy(orderByFunc);
    }
      
    var totalCount = queryable.Count();

    var results = queryable
      .ProviderSearch()
      .Skip((query.PageNumber - 1) * query.PageSize)
      .Take(query.PageSize)
      .ToList(); 
    
    int totalPages = (int)Math.Ceiling((double)totalCount / query.PageSize);
    var pageInfo = new PagedInfo(query.PageNumber, query.PageSize, totalPages, totalCount);
    var result = new PagedResult<List<ProviderSearchDto>>(pageInfo, results);
    return result;
  }
}
