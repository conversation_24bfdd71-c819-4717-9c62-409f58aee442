using System.Data.Common;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;

namespace Fiestr.Infrastructure.Data;

public class DbConnProvider(AppDbContext db) : IDbConnProvider
{
  public DbConnection GetDb()
  {
    return db.Database.GetDbConnection();
  }
  
  public string GetConnStr()
  {
    return db.Database.GetDbConnection().ConnectionString;
  }

  public DbTransaction? GetTxn()
  {
    return db.Database.CurrentTransaction?.GetDbTransaction();
  }
}
