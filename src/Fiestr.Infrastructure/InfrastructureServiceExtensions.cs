using System.Configuration;
using Ardalis.GuardClauses;
using Ardalis.SharedKernel;
using Fiestr.Core.Interfaces;
using Fiestr.Infrastructure.Configuration;
using Fiestr.Infrastructure.Data;
using Fiestr.Infrastructure.Data.Queries;
using Fiestr.Infrastructure.Data.QueryDb;
using Fiestr.Infrastructure.ImageProcessing;
using Fiestr.Infrastructure.OktaRestClient;
using Fiestr.Infrastructure.Security;
using Fiestr.Infrastructure.Services;
using Fiestr.UseCases.ImageDefinitions.QueryById;
using Fiestr.UseCases.Mgmt;
using Fiestr.UseCases.Products;
using Fiestr.UseCases.Products.AddImage;
using Fiestr.UseCases.Providers.Search;
using Fiestr.UseCases.Tags.List;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using ConfigurationManager = Microsoft.Extensions.Configuration.ConfigurationManager;

namespace Fiestr.Infrastructure;
public static class InfrastructureServiceExtensions
{
  public static IServiceCollection AddInfrastructureServices(
    this IServiceCollection services,
    ConfigurationManager config,
    ILogger logger)
  {
    string? connectionString = config.GetConnectionString("SqliteConnection");
    Guard.Against.Null(connectionString);
    services.AddDbContext<AppDbContext>(options => options.UseSqlite(connectionString));
    services.AddDbContext<QueryDbContext>(options => options.UseSqlite(connectionString));

    // Persistence
    services.AddScoped<IDbConnProvider, DbConnProvider>();
    services.AddScoped(typeof(IRepository<>), typeof(EfRepository<>));
    services.AddScoped(typeof(IReadRepository<>), typeof(EfRepository<>));
    
    // Query/Command services
    services.AddScoped<IListProductsForProviderQueryService, ProductQueryServices>();
    services.AddScoped<IGetProductByIdQueryService, ProductQueryServices>();
    
    // Img service
    services.AddScoped<IImageService, ImageService>();
    services.Configure<ImageConfiguration>(config.GetSection(ImageConfiguration.Position));
    
    // Auth/Okta
    services.Configure<FiestrJwtConfiguration>(config.GetSection("JwtConfiguration"));
    services.Configure<OktaConfiguration>(config.GetSection(OktaConfiguration.Location));
    services.AddSingleton<IOktaClient, OktaClient>();
    services.AddScoped<IUnitOfWork, UnitOfWork>();
    services.AddScoped<IAuthService, AuthService>();
    
    // Query service
    services.AddScoped<ICustomClaimsQueryService, CustomClaimsQueryService>();
    services.AddScoped<IProviderSearchQueryService, ProviderSearchQueryService>();
    services.AddScoped<IListTagsQueryService, ListTagsQueryService>();
    services.AddScoped<IUserCanEditProductQueryService, UserCanEditProductQueryService>();
    services.AddScoped<IImageDefinitionByIdQueryService, ImageDefinitionByIdQueryService>();
    
    // Misc. Services
    services.AddScoped<IImportService, ImportService>();
    
    // Options
    services.Configure<ConnectionStringConfiguration>(config.GetSection(ConnectionStringConfiguration.Location));

    // services.Configure<MailserverConfiguration>(config.GetSection("Mailserver"));
    logger.LogInformation("{Project} services registered", "Infrastructure");

    return services;
  }
}
