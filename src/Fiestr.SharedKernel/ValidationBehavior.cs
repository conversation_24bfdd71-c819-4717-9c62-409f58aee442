using Ardalis.Result;
using Fiestr.SharedKernel.Extensions;
using FluentValidation;
using FluentValidation.Results;
using MediatR;
namespace Fiestr.SharedKernel;

public class ValidationBehavior<TRequest, TResponse>(IEnumerable<IValidator<TRequest>> validators) : IPipelineBehavior<TRequest, TResponse>
  where TRequest : IRequest<TResponse>
  where TResponse: IResult
{
  public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next,
    CancellationToken ct)
  {
    if (validators.IsEmpty())
      return await next();

    var context = new ValidationContext<TRequest>(request);

    var validationResults = await Task.WhenAll(validators.Select(v =>
      v.ValidateAsync(context, ct)));
    
    var errors = validationResults
      .Where(r => r.Errors.Count != 0)
      .SelectMany(r => r.Errors)
      .ToList();
    var validationErrors = errors.Select(e => new ValidationError(e.PropertyName, e.<PERSON>r<PERSON>essage, e.ErrorCode, ValidationSeverity.Error)).ToList();

    if (errors.Count != 0)
    {
      if (typeof(TResponse).IsGenericType && typeof(TResponse).GetGenericTypeDefinition() == typeof(Result<>))
      {
        var resultType = typeof(TResponse).GetGenericArguments()[0];
        var invalidMethod = typeof(Result<>)
          .MakeGenericType(resultType)
          .GetMethod(nameof(Result<int>.Invalid), [typeof(List<ValidationError>)]);

        if (invalidMethod != null)
        {
          return (TResponse)invalidMethod.Invoke(null, [validationErrors])!;
        }
      }
      else if(typeof(TResponse) == typeof(Result))
        return (TResponse)(object)Result.Invalid(validationErrors);
      
      else
        throw new Exception(BuildErrorMessage(errors));
    }

    return await next();
  }
  
  private static string BuildErrorMessage(IEnumerable<ValidationFailure> errors)
  {
    var errorStr = string.Join($"{Environment.NewLine}- ", errors);
    var head = $"Validation failed, errors: {Environment.NewLine}";
    return head + errorStr;
  }
}
