using System.Text.RegularExpressions;
namespace Fiestr.SharedKernel.Extensions;

public static class StringExtensions
{
  public static string Camelize(this string str)
  {
    if (string.IsNullOrEmpty(str)) return str;

    var words = str.Split(["_", " "], StringSplitOptions.RemoveEmptyEntries)
      .Select((word, index) => index > 0 ?
        char.ToUpper(word[0]) + word.Substring(1).ToLower() :
        word.ToLower())
      .ToArray();

    return string.Join(string.Empty, words);
  }
  
  public static string Pascalize(this string input) =>
    Regex.Replace(input, @"(?:[ _-]+|^)([a-zA-Z])", match => match
      .Groups[1]
      .Value.ToUpper());

  
}
