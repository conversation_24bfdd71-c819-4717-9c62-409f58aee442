using Ardalis.Result;
namespace Fiestr.SharedKernel.Extensions;

public static class ResultExtensions
{
  public static Result MapToEmptyResult<T>(this Result<T> result)
  {
    // copied from Map in Ardalis.Result.ResultExtensions
    switch (result.Status)
    {
      case ResultStatus.Ok: return Result.Success();
      case ResultStatus.NotFound: return result.Errors.Any()
        ? Result.NotFound(result.Errors.ToArray())
        : Result.NotFound();
      case ResultStatus.Unauthorized: return Result.Unauthorized();
      case ResultStatus.Forbidden: return Result.Forbidden();
      case ResultStatus.Invalid: return Result.Invalid(result.ValidationErrors.ToList());
      case ResultStatus.Error: return Result.Error(new ErrorList(result.Errors));
      case ResultStatus.Conflict: return result.Errors.Any()
        ? Result.Conflict(result.Errors.ToArray())
        : Result.Conflict();
      case ResultStatus.CriticalError: return Result.CriticalError(result.Errors.ToArray());
      case ResultStatus.Unavailable: return Result.Unavailable(result.Errors.ToArray());
      default:
        throw new NotSupportedException($"Result {result.Status} conversion is not supported.");
    }  
  }
}
