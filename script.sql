CREATE TABLE IF NOT EXISTS "__EFMigrationsHistory" (
    "MigrationId" TEXT NOT NULL CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY,
    "ProductVersion" TEXT NOT NULL
);

BEGIN TRANSACTION;

CREATE TABLE "Image" (
    "Id" TEXT NOT NULL CONSTRAINT "PK_Image" PRIMARY KEY,
    "Data" BLOB NULL,
    "ContentType" TEXT NULL
);

CREATE TABLE "Providers" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Providers" PRIMARY KEY AUTOINCREMENT,
    "Name" TEXT NOT NULL,
    "PhoneNumber" TEXT NOT NULL
);

CREATE TABLE "Tags" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Tags" PRIMARY KEY AUTOINCREMENT,
    "Name" TEXT NOT NULL,
    "Category" TEXT NOT NULL,
    "EntityType" INTEGER NOT NULL
);

CREATE TABLE "Users" (
    "Id" TEXT NOT NULL CONSTRAINT "PK_Users" PRIMARY KEY,
    "OktaUserId" TEXT NOT NULL,
    "Name" TEXT NOT NULL,
    "Nickname" TEXT NOT NULL,
    "Email" TEXT NOT NULL
);

CREATE TABLE "Product" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Product" PRIMARY KEY AUTOINCREMENT,
    "ProviderId" INTEGER NOT NULL,
    "Name" TEXT NOT NULL,
    "Description" TEXT NULL,
    CONSTRAINT "FK_Product_Providers_ProviderId" FOREIGN KEY ("ProviderId") REFERENCES "Providers" ("Id") ON DELETE CASCADE
);

CREATE TABLE "ProviderImage" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_ProviderImage" PRIMARY KEY AUTOINCREMENT,
    "ThumbnailId" TEXT NOT NULL,
    "ImageId" TEXT NOT NULL,
    "OwnerId" INTEGER NOT NULL,
    CONSTRAINT "FK_ProviderImage_Image_ImageId" FOREIGN KEY ("ImageId") REFERENCES "Image" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_ProviderImage_Image_ThumbnailId" FOREIGN KEY ("ThumbnailId") REFERENCES "Image" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_ProviderImage_Providers_OwnerId" FOREIGN KEY ("OwnerId") REFERENCES "Providers" ("Id") ON DELETE CASCADE
);

CREATE TABLE "ProviderTags" (
    "ProviderId" INTEGER NOT NULL,
    "TagId" INTEGER NOT NULL,
    CONSTRAINT "PK_ProviderTags" PRIMARY KEY ("ProviderId", "TagId"),
    CONSTRAINT "FK_ProviderTags_Providers_ProviderId" FOREIGN KEY ("ProviderId") REFERENCES "Providers" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_ProviderTags_Tags_TagId" FOREIGN KEY ("TagId") REFERENCES "Tags" ("Id") ON DELETE CASCADE
);

CREATE TABLE "ProviderUsers" (
    "UserId" TEXT NOT NULL,
    "ProviderId" INTEGER NOT NULL,
    CONSTRAINT "PK_ProviderUsers" PRIMARY KEY ("ProviderId", "UserId"),
    CONSTRAINT "FK_ProviderUsers_Providers_ProviderId" FOREIGN KEY ("ProviderId") REFERENCES "Providers" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_ProviderUsers_Users_UserId" FOREIGN KEY ("UserId") REFERENCES "Users" ("Id") ON DELETE CASCADE
);

CREATE TABLE "ProductImage" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_ProductImage" PRIMARY KEY AUTOINCREMENT,
    "ThumbnailId" TEXT NOT NULL,
    "ImageId" TEXT NOT NULL,
    "OwnerId" INTEGER NOT NULL,
    CONSTRAINT "FK_ProductImage_Image_ImageId" FOREIGN KEY ("ImageId") REFERENCES "Image" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_ProductImage_Image_ThumbnailId" FOREIGN KEY ("ThumbnailId") REFERENCES "Image" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_ProductImage_Product_OwnerId" FOREIGN KEY ("OwnerId") REFERENCES "Product" ("Id") ON DELETE CASCADE
);

CREATE INDEX "IX_Product_ProviderId" ON "Product" ("ProviderId");

CREATE INDEX "IX_ProductImage_ImageId" ON "ProductImage" ("ImageId");

CREATE INDEX "IX_ProductImage_OwnerId" ON "ProductImage" ("OwnerId");

CREATE INDEX "IX_ProductImage_ThumbnailId" ON "ProductImage" ("ThumbnailId");

CREATE INDEX "IX_ProviderImage_ImageId" ON "ProviderImage" ("ImageId");

CREATE INDEX "IX_ProviderImage_OwnerId" ON "ProviderImage" ("OwnerId");

CREATE INDEX "IX_ProviderImage_ThumbnailId" ON "ProviderImage" ("ThumbnailId");

CREATE INDEX "IX_ProviderTags_TagId" ON "ProviderTags" ("TagId");

CREATE INDEX "IX_ProviderUsers_UserId" ON "ProviderUsers" ("UserId");

CREATE INDEX "IX_User_OktaUserId" ON "Users" ("OktaUserId");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240408224657_Initial', '8.0.3');

COMMIT;

