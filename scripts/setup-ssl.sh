#!/bin/bash
set -euo pipefail

DOMAIN="www.fiestr.com"
EMAIL="<EMAIL>"  # Change this to your email

echo "🔐 Setting up SSL certificate for $DOMAIN..."

# Install certbot if not already installed
if ! command -v certbot &> /dev/null; then
    echo "📦 Installing certbot..."
    sudo apt update
    sudo apt install -y certbot
fi

# Stop the application temporarily to free up port 80
echo "⏸️  Stopping FiestrBackend service temporarily..."
sudo systemctl stop FiestrBackend || true

# Generate SSL certificate using Let's Encrypt
echo "🔑 Generating SSL certificate..."
sudo certbot certonly --standalone \
    --non-interactive \
    --agree-tos \
    --email "$EMAIL" \
    -d "$DOMAIN"

# Convert the certificate to PKCS#12 format for .NET
echo "🔄 Converting certificate to PKCS#12 format..."
sudo openssl pkcs12 -export \
    -out "/etc/letsencrypt/live/$DOMAIN/certificate.p12" \
    -inkey "/etc/letsencrypt/live/$DOMAIN/privkey.pem" \
    -in "/etc/letsencrypt/live/$DOMAIN/cert.pem" \
    -certfile "/etc/letsencrypt/live/$DOMAIN/chain.pem" \
    -password pass:

# Set proper permissions
sudo chown azureuser:azureuser "/etc/letsencrypt/live/$DOMAIN/certificate.p12"
sudo chmod 600 "/etc/letsencrypt/live/$DOMAIN/certificate.p12"

# Create a systemd override to set the certificate path
sudo mkdir -p /etc/systemd/system/FiestrBackend.service.d
sudo tee /etc/systemd/system/FiestrBackend.service.d/override.conf > /dev/null <<EOF
[Service]
Environment=ASPNETCORE_Kestrel__Certificates__Default__Path=/etc/letsencrypt/live/$DOMAIN/certificate.p12
Environment=ASPNETCORE_Kestrel__Certificates__Default__Password=
EOF

# Reload systemd and restart the service
sudo systemctl daemon-reload
sudo systemctl start FiestrBackend

echo "✅ SSL certificate setup complete!"
echo "🌐 Your application should now be accessible at:"
echo "   HTTP:  http://$DOMAIN:5000"
echo "   HTTPS: https://$DOMAIN:5001"

# Set up automatic certificate renewal
echo "🔄 Setting up automatic certificate renewal..."
(sudo crontab -l 2>/dev/null; echo "0 12 * * * /usr/bin/certbot renew --quiet --post-hook 'systemctl restart FiestrBackend'") | sudo crontab -

echo "✅ Automatic certificate renewal configured!"
