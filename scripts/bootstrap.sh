#!/bin/bash
set -euo pipefail

APP_NAME="FiestrBackend"
APP_DIR="/var/www/$APP_NAME"
INSTALL_DIR="$APP_DIR/dotnet"
SERVICE_FILE="/etc/systemd/system/$APP_NAME.service"
DOTNET="$INSTALL_DIR/dotnet"
DOTNET_VERSION="8.0"

echo "🧼 Setting permissions on $APP_DIR..."
sudo mkdir -p "$APP_DIR"
sudo chown -R azureuser:azureuser "$APP_DIR"
sudo chmod -R 755 "$APP_DIR"

# Check for dotnet
if ! command -v dotnet &> /dev/null; then
  echo "🚀 .NET not found — installing runtime $DOTNET_VERSION..."

  echo "⬇️  Downloading dotnet-install.sh..."
  for i in {1..3}; do
    wget -q https://dotnet.microsoft.com/download/dotnet/scripts/v1/dotnet-install.sh -O dotnet-install.sh && break
    echo "Retry $i failed. Retrying in 2s..."
    sleep 2
  done

  if [ ! -f dotnet-install.sh ]; then
    echo "❌ Failed to download dotnet-install.sh after 3 attempts."
    exit 1
  fi

  chmod +x dotnet-install.sh
  ./dotnet-install.sh --channel "$DOTNET_VERSION" --runtime aspnetcore --install-dir "$INSTALL_DIR"
  sudo ln -sf "$DOTNET" /usr/bin/dotnet

  echo "✅ .NET runtime installed at $INSTALL_DIR"
else
  echo "✅ .NET already installed: $(dotnet --version)"
fi

# 🔐 SSL Certificate Setup (Idempotent)
setup_ssl() {
  local DOMAIN="www.fiestr.com"
  local CERT_PATH="/etc/letsencrypt/live/$DOMAIN"
  local P12_PATH="$CERT_PATH/certificate.p12"
  
  echo "🔐 Checking SSL certificate for $DOMAIN..."

  # Debug: List what's actually in the letsencrypt directory
  echo "🔍 Debugging certificate paths..."
  if [ -d "/etc/letsencrypt" ]; then
    echo "📁 /etc/letsencrypt exists"
    if [ -d "/etc/letsencrypt/live" ]; then
      echo "📁 /etc/letsencrypt/live exists"
      echo "📋 Contents of /etc/letsencrypt/live:"
      sudo ls -la /etc/letsencrypt/live/ || echo "❌ Cannot list /etc/letsencrypt/live/"

      if [ -d "/etc/letsencrypt/live/$DOMAIN" ]; then
        echo "📁 Certificate directory exists: $CERT_PATH"
        echo "📋 Contents of certificate directory:"
        sudo ls -la "$CERT_PATH/" || echo "❌ Cannot list certificate directory"
      else
        echo "❌ Certificate directory does not exist: $CERT_PATH"
        echo "📋 Available domains in /etc/letsencrypt/live/:"
        sudo ls -1 /etc/letsencrypt/live/ 2>/dev/null || echo "❌ No domains found"
      fi
    else
      echo "❌ /etc/letsencrypt/live does not exist"
    fi
  else
    echo "❌ /etc/letsencrypt does not exist"
  fi

  # Check what certbot knows about
  if command -v certbot &> /dev/null; then
    echo "🔍 Checking certbot certificates..."
    sudo certbot certificates 2>/dev/null || echo "❌ Cannot list certbot certificates"

    # Check renewal configuration
    if [ -d "/etc/letsencrypt/renewal" ]; then
      echo "📋 Renewal configurations found:"
      sudo ls -la /etc/letsencrypt/renewal/ || echo "❌ Cannot list renewal configs"

      # Look for our domain's renewal config
      if [ -f "/etc/letsencrypt/renewal/www.fiestr.com.conf" ]; then
        echo "📋 Found renewal config for www.fiestr.com:"
        sudo cat /etc/letsencrypt/renewal/www.fiestr.com.conf | grep -E "(cert|key|chain)" || echo "❌ Cannot read renewal config"
      fi
    fi
  else
    echo "❌ Certbot not installed"
  fi

  # Check if certificate directory exists (using sudo for permissions)
  if sudo test -d "$CERT_PATH" && sudo test -f "$CERT_PATH/cert.pem"; then
    echo "📋 Certificate found, checking validity..."
    
    # Get certificate expiration date (using sudo)
    local EXPIRY_DATE=$(sudo openssl x509 -enddate -noout -in "$CERT_PATH/cert.pem" | cut -d= -f2)
    echo "📅 Certificate expires: $EXPIRY_DATE"

    # Check if certificate is still valid (more than 30 days remaining)
    if sudo openssl x509 -checkend 2592000 -noout -in "$CERT_PATH/cert.pem" 2>/dev/null; then
      echo "✅ Valid SSL certificate already exists and is not due for renewal"
      
      # Ensure P12 certificate exists
      if [ ! -f "$P12_PATH" ]; then
        echo "🔄 Converting existing certificate to PKCS#12 format..."
        sudo openssl pkcs12 -export \
          -out "$P12_PATH" \
          -inkey "$CERT_PATH/privkey.pem" \
          -in "$CERT_PATH/cert.pem" \
          -certfile "$CERT_PATH/chain.pem" \
          -password pass:
        sudo chown azureuser:azureuser "$P12_PATH"
        sudo chmod 600 "$P12_PATH"
        echo "✅ P12 certificate created successfully"

        # Also ensure the certificate directory is accessible
        sudo chmod 755 "$CERT_PATH"
        sudo chmod 644 "$CERT_PATH"/*.pem 2>/dev/null || true
      else
        echo "✅ P12 certificate already exists"
      fi
      
      # Verify P12 certificate is readable
      if sudo openssl pkcs12 -info -in "$P12_PATH" -noout -passin pass: 2>/dev/null; then
        echo "✅ P12 certificate is valid and readable"
        return 0
      else
        echo "⚠️  P12 certificate exists but is not readable, recreating..."
        sudo rm -f "$P12_PATH"
        sudo openssl pkcs12 -export \
          -out "$P12_PATH" \
          -inkey "$CERT_PATH/privkey.pem" \
          -in "$CERT_PATH/cert.pem" \
          -certfile "$CERT_PATH/chain.pem" \
          -password pass:
        sudo chown azureuser:azureuser "$P12_PATH"
        sudo chmod 600 "$P12_PATH"
        echo "✅ P12 certificate recreated successfully"

        # Also ensure the certificate directory is accessible
        sudo chmod 755 "$CERT_PATH"
        sudo chmod 644 "$CERT_PATH"/*.pem 2>/dev/null || true
        return 0
      fi
    else
      echo "⚠️  Certificate exists but expires soon, will renew..."
    fi
  else
    echo "❌ No certificate found at $CERT_PATH"

    # Try to find certificates in alternative locations
    echo "🔍 Searching for certificates in alternative locations..."

    # Check if there are any certificates in /etc/letsencrypt/live/
    if sudo test -d "/etc/letsencrypt/live"; then
      for cert_dir in $(sudo find /etc/letsencrypt/live -maxdepth 1 -type d -name "*" | tail -n +2); do
        if sudo test -d "$cert_dir" && sudo test -f "$cert_dir/cert.pem"; then
          local alt_domain=$(basename "$cert_dir")
          echo "📋 Found certificate for domain: $alt_domain"

          # If this is our domain or a related domain, use it
          if [[ "$alt_domain" == *"fiestr.com"* ]]; then
            echo "✅ Found matching certificate for $alt_domain"
            CERT_PATH="$cert_dir"
            P12_PATH="$CERT_PATH/certificate.p12"

            # Update the certificate environment variable
            CERT_ENV_VARS="Environment=ASPNETCORE_Kestrel__Certificates__Default__Path=$P12_PATH
Environment=ASPNETCORE_Kestrel__Certificates__Default__Password="

            # Create P12 certificate
            if [ ! -f "$P12_PATH" ]; then
              echo "🔄 Converting found certificate to PKCS#12 format..."
              sudo openssl pkcs12 -export \
                -out "$P12_PATH" \
                -inkey "$CERT_PATH/privkey.pem" \
                -in "$CERT_PATH/cert.pem" \
                -certfile "$CERT_PATH/chain.pem" \
                -password pass:
              sudo chown azureuser:azureuser "$P12_PATH"
              sudo chmod 600 "$P12_PATH"

              # Also ensure the certificate directory is accessible
              sudo chmod 755 "$CERT_PATH"
              sudo chmod 644 "$CERT_PATH"/*.pem 2>/dev/null || true
            fi

            return 0
          fi
        fi
      done
    fi
  fi

  # If we get here, we need to generate a new certificate
  echo "🔑 Certificate generation/renewal needed but skipping for now..."
  echo "ℹ️  Existing certificate will be used if available"

  # Check if we have any certificate files at all
  if sudo test -f "$CERT_PATH/cert.pem"; then
    # Try to create P12 even if certificate is expiring soon
    if [ ! -f "$P12_PATH" ]; then
      echo "🔄 Creating P12 certificate from existing files..."
      sudo openssl pkcs12 -export \
        -out "$P12_PATH" \
        -inkey "$CERT_PATH/privkey.pem" \
        -in "$CERT_PATH/cert.pem" \
        -certfile "$CERT_PATH/chain.pem" \
        -password pass:
      sudo chown azureuser:azureuser "$P12_PATH"
      sudo chmod 600 "$P12_PATH"

      # Also ensure the certificate directory is accessible
      sudo chmod 755 "$CERT_PATH"
      sudo chmod 644 "$CERT_PATH"/*.pem 2>/dev/null || true
    fi
    return 0
  fi
  
  return 1
}

# Run SSL setup
if setup_ssl; then
  SSL_SUCCESS=true
  CERT_ENV_VARS="Environment=ASPNETCORE_Kestrel__Certificates__Default__Path=/etc/letsencrypt/live/www.fiestr.com/certificate.p12
Environment=ASPNETCORE_Kestrel__Certificates__Default__Password="
  echo "✅ SSL configuration will be enabled"
else
  SSL_SUCCESS=false
  CERT_ENV_VARS=""
  echo "⚠️  SSL configuration will be disabled"
fi

# Create systemd service
echo "⚙️ Creating/updating systemd service: $APP_NAME"
if [ "$SSL_SUCCESS" = true ]; then
  URLS="Environment=ASPNETCORE_URLS=http://+:5000;https://+:5001"
  echo "🔒 Service will listen on both HTTP (5000) and HTTPS (5001)"
else
  URLS="Environment=ASPNETCORE_URLS=http://+:5000"
  echo "🌐 Service will listen on HTTP only (5000)"
fi

sudo tee "$SERVICE_FILE" > /dev/null <<EOF
[Unit]
Description=.NET Web API - $APP_NAME
After=network.target

[Service]
WorkingDirectory=$APP_DIR/published
ExecStart=/usr/bin/dotnet $APP_DIR/published/Fiestr.Web.dll
Restart=always
RestartSec=10
SyslogIdentifier=$APP_NAME
User=azureuser
Environment=DOTNET_ENVIRONMENT=Production
$URLS
$CERT_ENV_VARS

[Install]
WantedBy=multi-user.target
EOF

sudo systemctl daemon-reexec
sudo systemctl daemon-reload
sudo systemctl enable "$APP_NAME"

# Set up automatic certificate renewal (only if SSL was successful)
if [ "$SSL_SUCCESS" = true ]; then
  setup_auto_renewal() {
    local CRON_JOB="0 12 * * * /usr/bin/certbot renew --quiet --post-hook 'systemctl restart FiestrBackend'"
    
    # Check if cron job already exists
    if sudo crontab -l 2>/dev/null | grep -q "certbot renew"; then
      echo "✅ Certificate auto-renewal already configured"
    else
      echo "🔄 Setting up automatic certificate renewal..."
      (sudo crontab -l 2>/dev/null; echo "$CRON_JOB") | sudo crontab -
      echo "✅ Automatic certificate renewal configured!"
    fi
  }
  
  setup_auto_renewal
fi

# ✅ Open ports for external access
echo "🌐 Configuring firewall..."
sudo ufw allow 5000
if [ "$SSL_SUCCESS" = true ]; then
  sudo ufw allow 5001
  echo "✅ Ports 5000 (HTTP) and 5001 (HTTPS) are open"
else
  echo "✅ Port 5000 (HTTP) is open"
fi

echo "🎉 Bootstrap complete!"
echo "🌐 Your application will be accessible at:"
echo "   HTTP:  http://www.fiestr.com:5000"
if [ "$SSL_SUCCESS" = true ]; then
  echo "   HTTPS: https://www.fiestr.com:5001"
  echo ""
  echo "🔒 SSL certificate information:"
  if [ -f "/etc/letsencrypt/live/www.fiestr.com/cert.pem" ]; then
    openssl x509 -enddate -noout -in "/etc/letsencrypt/live/www.fiestr.com/cert.pem"
  fi
else
  echo "   HTTPS: Not available (SSL setup failed)"
fi
