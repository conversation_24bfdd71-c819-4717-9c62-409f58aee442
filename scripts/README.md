# Deployment Scripts

## bootstrap.sh

This script handles the complete setup and deployment of the Fiestr backend application. It is designed to be **idempotent**, meaning it can be run multiple times safely without causing issues.

### What it does:

1. **Sets up directory permissions** for the application
2. **Installs .NET runtime** if not already present
3. **SSL Certificate Management** (idempotent):
   - Checks if a valid SSL certificate exists
   - Generates a new certificate using Let's Encrypt if needed
   - Renews certificates that are expiring soon (< 30 days)
   - Converts certificates to PKCS#12 format for .NET Core
4. **Creates/updates systemd service** with proper environment variables
5. **Sets up automatic certificate renewal** via cron job
6. **Opens firewall ports** 5000 (HTTP) and 5001 (HTTPS)

### SSL Certificate Configuration

The script automatically:
- Uses `www.fiestr.com` as the domain (configurable in the script)
- Sets up Let's Encrypt certificates
- Configures automatic renewal
- Handles certificate format conversion for .NET Core

### Environment Variables Set

The systemd service is configured with:
- `DOTNET_ENVIRONMENT=Production`
- `ASPNETCORE_URLS=http://+:5000;https://+:5001`
- `ASPNETCORE_Kestrel__Certificates__Default__Path=/etc/letsencrypt/live/www.fiestr.com/certificate.p12`
- `ASPNETCORE_Kestrel__Certificates__Default__Password=` (empty password)

### Usage

The script is automatically run by the Azure DevOps pipeline during deployment. It can also be run manually:

```bash
chmod +x /var/www/FiestrBackend/scripts/bootstrap.sh
/var/www/FiestrBackend/scripts/bootstrap.sh
```

### Customization

To use a different domain, update the `DOMAIN` and `EMAIL` variables at the top of the `setup_ssl()` function.
