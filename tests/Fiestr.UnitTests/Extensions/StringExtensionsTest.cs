using Fiestr.SharedKernel.Extensions;
using Xunit;

namespace Fiestr.UnitTests.Extensions;

[Collection("StringExtensionsTest")]
public class StringExtensionsTest
{
  [Fact]
  public void ToCamelCase_GivenLowerCaseValue_ReturnsSameValue()
  {
    const string value = "testvalue";
    var result = value.Camelize();
    Assert.Equal(value, result);
  }

  [Fact]
  public void ToCamelCase_GivenUpperCaseValue_ReturnsLowerCaseValue()
  {
    const string value = "TESTVALUE";
    const string expected = "testvalue";
    var result = value.Camelize();
    Assert.Equal(expected, result);
  }

  [Fact]
  public void ToCamelCase_GivenPascalCaseValue_ReturnsCamelCaseValue()
  {
    const string value = "TestValue";
    const string expected = "testvalue";
    var result = value.Camelize();
    Assert.Equal(expected, result);
  }

  [Fact]
  public void ToCamelCase_GivenMixedCaseValue_ReturnsCamelCaseValue()
  {
    const string value = "TesTVAlue";
    const string expected = "testvalue";
    var result = value.Camelize();
    Assert.Equal(expected, result);
  }

  [Fact]
  public void ToCamelCase_GivenStringWithSpaces_ReturnsCamelCaseValue()
  {
    var value = "Test Value With Spaces";
    var expected = "testValueWithSpaces";
    var result = value.Camelize();
    Assert.Equal(expected, result);
  }
}
